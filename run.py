#!/usr/bin/env python3
"""
ملف تشغيل برنامج مصروف
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.main import main
    
    if __name__ == "__main__":
        print("🚀 بدء تشغيل برنامج مصروف...")
        print("💰 برنامج إدارة المصروفات الشخصية مع الذكاء الصناعي")
        print("=" * 50)
        main()
        
except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبات: {e}")
    print("💡 تأكد من تثبيت المتطلبات باستخدام: pip install -r requirements.txt")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ خطأ في تشغيل البرنامج: {e}")
    sys.exit(1)
