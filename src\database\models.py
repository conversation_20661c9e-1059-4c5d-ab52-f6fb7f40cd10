"""
نماذج قاعدة البيانات
"""
from dataclasses import dataclass
from datetime import datetime, date
from typing import Optional, List
from enum import Enum

class TransactionType(Enum):
    """أنواع المعاملات المالية"""
    INCOME = "income"      # دخل
    EXPENSE = "expense"    # مصروف
    TRANSFER = "transfer"  # تحويل

class DebtStatus(Enum):
    """حالة الدين"""
    ACTIVE = "active"      # نشط
    PAID = "paid"         # مسدد
    OVERDUE = "overdue"   # متأخر

class PaymentMethod(Enum):
    """طرق الدفع"""
    CASH = "cash"         # نقدي
    CARD = "card"         # بطاقة
    BANK = "bank"         # تحويل بنكي
    DIGITAL = "digital"   # محفظة رقمية

@dataclass
class User:
    """نموذج المستخدم"""
    id: Optional[int] = None
    username: str = ""
    email: str = ""
    password_hash: str = ""
    full_name: str = ""
    created_at: datetime = None
    last_login: datetime = None
    is_active: bool = True
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Category:
    """نموذج الفئة"""
    id: Optional[int] = None
    name: str = ""
    type: TransactionType = TransactionType.EXPENSE
    color: str = "#3498db"
    icon: str = "💰"
    description: str = ""
    is_active: bool = True
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Account:
    """نموذج الحساب المالي"""
    id: Optional[int] = None
    name: str = ""
    type: str = "cash"  # cash, bank, card, digital
    balance: float = 0.0
    currency: str = "IQD"
    description: str = ""
    is_active: bool = True
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Transaction:
    """نموذج المعاملة المالية"""
    id: Optional[int] = None
    type: TransactionType = TransactionType.EXPENSE
    amount: float = 0.0
    description: str = ""
    category_id: int = 0
    account_id: int = 0
    payment_method: PaymentMethod = PaymentMethod.CASH
    date: date = None
    created_at: datetime = None
    notes: str = ""
    receipt_path: str = ""
    tags: str = ""  # مفصولة بفواصل
    
    def __post_init__(self):
        if self.date is None:
            self.date = date.today()
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Debt:
    """نموذج الدين"""
    id: Optional[int] = None
    name: str = ""
    type: str = "personal"  # personal, loan, credit_card, etc.
    total_amount: float = 0.0
    remaining_amount: float = 0.0
    interest_rate: float = 0.0
    monthly_payment: float = 0.0
    start_date: date = None
    due_date: date = None
    status: DebtStatus = DebtStatus.ACTIVE
    creditor: str = ""  # الدائن
    description: str = ""
    created_at: datetime = None
    
    def __post_init__(self):
        if self.start_date is None:
            self.start_date = date.today()
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.remaining_amount == 0.0:
            self.remaining_amount = self.total_amount

@dataclass
class DebtPayment:
    """نموذج دفعة الدين"""
    id: Optional[int] = None
    debt_id: int = 0
    amount: float = 0.0
    payment_date: date = None
    interest_amount: float = 0.0
    principal_amount: float = 0.0
    notes: str = ""
    created_at: datetime = None
    
    def __post_init__(self):
        if self.payment_date is None:
            self.payment_date = date.today()
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Budget:
    """نموذج الميزانية"""
    id: Optional[int] = None
    name: str = ""
    category_id: int = 0
    amount: float = 0.0
    period: str = "monthly"  # daily, weekly, monthly, yearly
    start_date: date = None
    end_date: date = None
    is_active: bool = True
    created_at: datetime = None
    
    def __post_init__(self):
        if self.start_date is None:
            self.start_date = date.today()
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Goal:
    """نموذج الهدف المالي"""
    id: Optional[int] = None
    name: str = ""
    target_amount: float = 0.0
    current_amount: float = 0.0
    target_date: date = None
    description: str = ""
    is_achieved: bool = False
    created_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Notification:
    """نموذج الإشعار"""
    id: Optional[int] = None
    title: str = ""
    message: str = ""
    type: str = "info"  # info, warning, error, success
    is_read: bool = False
    created_at: datetime = None
    scheduled_at: datetime = None
    
    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()

@dataclass
class Settings:
    """نموذج الإعدادات"""
    id: Optional[int] = None
    key: str = ""
    value: str = ""
    type: str = "string"  # string, int, float, bool, json
    description: str = ""
    updated_at: datetime = None
    
    def __post_init__(self):
        if self.updated_at is None:
            self.updated_at = datetime.now()
