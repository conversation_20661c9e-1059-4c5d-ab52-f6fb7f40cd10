"""
مدير التصدير والتقارير
"""
import logging
import pandas as pd
from datetime import datetime, date
from pathlib import Path
from typing import List, Dict, Any, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
import arabic_reshaper
from bidi.algorithm import get_display

from ..config.settings import EXPORT_CONFIG, CURRENCY_CONFIG

class ExportManager:
    """مدير التصدير والتقارير"""
    
    def __init__(self):
        """تهيئة مدير التصدير"""
        self.logger = logging.getLogger(__name__)
        self.export_path = EXPORT_CONFIG['export_path']
        
        # إنشاء مجلد التصدير إذا لم يكن موجوداً
        self.export_path.mkdir(exist_ok=True)
        
        # إعداد matplotlib للغة العربية
        plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
        
    def export_transactions_to_excel(self, transactions: List[Dict[str, Any]], 
                                   filename: str = None) -> str:
        """تصدير المعاملات إلى ملف Excel"""
        try:
            if not transactions:
                raise ValueError("لا توجد معاملات للتصدير")
            
            # إنشاء DataFrame
            df = pd.DataFrame(transactions)
            
            # تنسيق التواريخ
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date']).dt.strftime('%Y-%m-%d')
            
            # تنسيق المبالغ
            if 'amount' in df.columns:
                df['amount'] = df['amount'].apply(lambda x: f"{x:,.0f}")
            
            # ترجمة أسماء الأعمدة
            column_translations = {
                'id': 'الرقم',
                'type': 'النوع',
                'amount': 'المبلغ',
                'description': 'الوصف',
                'category': 'الفئة',
                'date': 'التاريخ',
                'payment_method': 'طريقة الدفع',
                'notes': 'ملاحظات'
            }
            
            df = df.rename(columns=column_translations)
            
            # إنشاء اسم الملف
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"transactions_{timestamp}.xlsx"
            
            filepath = self.export_path / filename
            
            # تصدير إلى Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='المعاملات', index=False)
                
                # تنسيق الورقة
                worksheet = writer.sheets['المعاملات']
                
                # تعديل عرض الأعمدة
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            self.logger.info(f"تم تصدير المعاملات إلى: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير المعاملات إلى Excel: {e}")
            raise
    
    def export_debts_to_excel(self, debts: List[Dict[str, Any]], 
                            filename: str = None) -> str:
        """تصدير الديون إلى ملف Excel"""
        try:
            if not debts:
                raise ValueError("لا توجد ديون للتصدير")
            
            # إنشاء DataFrame
            df = pd.DataFrame(debts)
            
            # تنسيق التواريخ
            date_columns = ['start_date', 'due_date']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col]).dt.strftime('%Y-%m-%d')
            
            # تنسيق المبالغ
            amount_columns = ['total_amount', 'remaining_amount', 'monthly_payment']
            for col in amount_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: f"{x:,.0f}")
            
            # ترجمة أسماء الأعمدة
            column_translations = {
                'id': 'الرقم',
                'name': 'اسم الدين',
                'type': 'النوع',
                'total_amount': 'المبلغ الإجمالي',
                'remaining_amount': 'المبلغ المتبقي',
                'interest_rate': 'معدل الفائدة',
                'monthly_payment': 'القسط الشهري',
                'start_date': 'تاريخ البداية',
                'due_date': 'تاريخ الاستحقاق',
                'status': 'الحالة',
                'creditor': 'الدائن'
            }
            
            df = df.rename(columns=column_translations)
            
            # إنشاء اسم الملف
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"debts_{timestamp}.xlsx"
            
            filepath = self.export_path / filename
            
            # تصدير إلى Excel
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='الديون', index=False)
            
            self.logger.info(f"تم تصدير الديون إلى: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير الديون إلى Excel: {e}")
            raise
    
    def create_expense_chart(self, expenses_by_category: Dict[str, float], 
                           filename: str = None) -> str:
        """إنشاء رسم بياني للمصروفات حسب الفئة"""
        try:
            if not expenses_by_category:
                raise ValueError("لا توجد بيانات مصروفات لإنشاء الرسم البياني")
            
            # إعداد البيانات
            categories = list(expenses_by_category.keys())
            amounts = list(expenses_by_category.values())
            
            # إنشاء الرسم البياني
            plt.figure(figsize=(12, 8))
            
            # رسم دائري
            plt.subplot(1, 2, 1)
            colors_list = plt.cm.Set3(range(len(categories)))
            plt.pie(amounts, labels=categories, autopct='%1.1f%%', colors=colors_list)
            plt.title('توزيع المصروفات حسب الفئة', fontsize=14, fontweight='bold')
            
            # رسم بياني عمودي
            plt.subplot(1, 2, 2)
            bars = plt.bar(categories, amounts, color=colors_list)
            plt.title('المصروفات حسب الفئة', fontsize=14, fontweight='bold')
            plt.xlabel('الفئة')
            plt.ylabel('المبلغ (د.ع)')
            plt.xticks(rotation=45, ha='right')
            
            # إضافة قيم على الأعمدة
            for bar, amount in zip(bars, amounts):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(amounts)*0.01,
                        f'{amount:,.0f}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            # حفظ الرسم البياني
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"expense_chart_{timestamp}.png"
            
            filepath = self.export_path / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"تم إنشاء رسم بياني للمصروفات: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رسم بياني للمصروفات: {e}")
            raise
    
    def create_income_trend_chart(self, income_data: Dict[str, float], 
                                filename: str = None) -> str:
        """إنشاء رسم بياني لتطور الدخل"""
        try:
            if not income_data:
                raise ValueError("لا توجد بيانات دخل لإنشاء الرسم البياني")
            
            # إعداد البيانات
            dates = list(income_data.keys())
            amounts = list(income_data.values())
            
            # تحويل التواريخ
            date_objects = [datetime.strptime(d, '%Y-%m-%d') for d in dates]
            
            # إنشاء الرسم البياني
            plt.figure(figsize=(12, 6))
            plt.plot(date_objects, amounts, marker='o', linewidth=2, markersize=6)
            plt.title('تطور الدخل عبر الزمن', fontsize=14, fontweight='bold')
            plt.xlabel('التاريخ')
            plt.ylabel('المبلغ (د.ع)')
            plt.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            
            # تنسيق المحور الصادي
            plt.ticklabel_format(style='plain', axis='y')
            
            plt.tight_layout()
            
            # حفظ الرسم البياني
            if filename is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"income_trend_{timestamp}.png"
            
            filepath = self.export_path / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"تم إنشاء رسم بياني لتطور الدخل: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رسم بياني لتطور الدخل: {e}")
            raise
    
    def export_to_csv(self, data: List[Dict[str, Any]], filename: str) -> str:
        """تصدير البيانات إلى ملف CSV"""
        try:
            if not data:
                raise ValueError("لا توجد بيانات للتصدير")
            
            df = pd.DataFrame(data)
            filepath = self.export_path / filename
            
            # تصدير مع دعم UTF-8 للغة العربية
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"تم تصدير البيانات إلى CSV: {filepath}")
            return str(filepath)
            
        except Exception as e:
            self.logger.error(f"خطأ في تصدير البيانات إلى CSV: {e}")
            raise
    
    def format_arabic_text(self, text: str) -> str:
        """تنسيق النص العربي للعرض الصحيح"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text
    
    def get_export_path(self) -> str:
        """الحصول على مسار مجلد التصدير"""
        return str(self.export_path)
    
    def cleanup_old_exports(self, days_old: int = 30):
        """تنظيف ملفات التصدير القديمة"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            for file_path in self.export_path.iterdir():
                if file_path.is_file():
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime)
                    if file_time < cutoff_date:
                        file_path.unlink()
                        self.logger.info(f"تم حذف ملف التصدير القديم: {file_path}")
            
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف ملفات التصدير القديمة: {e}")

# مثيل عام لمدير التصدير
export_manager = ExportManager()
