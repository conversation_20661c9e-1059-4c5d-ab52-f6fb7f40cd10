# مصروف - برنامج إدارة المصروفات الشخصية

## 📖 نظرة عامة
برنامج مكتبي متطور لإدارة المصروفات الشخصية مع دعم الذكاء الصناعي، مصمم خصيصاً للمستخدمين العرب مع دعم كامل للدينار العراقي.

## ✨ الميزات الرئيسية
- 📊 تتبع الدخل والمصروفات (يومي، أسبوعي، شهري، سنوي)
- 💳 إدارة الديون والأقساط مع تنبيهات الاستحقاق
- 🤖 تحليلات ذكية ونصائح مالية بالذكاء الصناعي
- 💰 دعم كامل للدينار العراقي (IQD)
- 🔔 تنبيهات تلقائية للمدفوعات والمواعيد المهمة
- 📈 تقارير مفصلة ورسوم بيانية تفاعلية
- 🔒 حماية البيانات بتشفير متقدم
- 📱 واجهة سهلة الاستخدام تدعم اللغة العربية

## 🛠️ التقنيات المستخدمة
- **Python 3.11+** - اللغة الأساسية
- **CustomTkinter** - واجهة المستخدم الحديثة
- **SQLite** - قاعدة البيانات المحلية
- **scikit-learn** - التعلم الآلي والتنبؤات
- **pandas & numpy** - معالجة البيانات
- **matplotlib & plotly** - الرسوم البيانية

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.11 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

### خطوات التثبيت
1. استنساخ المشروع:
```bash
git clone https://github.com/your-username/masroof.git
cd masroof
```

2. إنشاء بيئة افتراضية:
```bash
python -m venv venv
source venv/bin/activate  # على Linux/macOS
# أو
venv\Scripts\activate     # على Windows
```

3. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

4. تشغيل البرنامج:
```bash
python src/main.py
```

## 📁 هيكل المشروع
```
masroof/
├── src/                    # الكود المصدري
│   ├── main.py            # نقطة البداية
│   ├── database/          # إدارة قاعدة البيانات
│   ├── ui/                # واجهة المستخدم
│   ├── ai/                # الذكاء الصناعي
│   ├── utils/             # الأدوات المساعدة
│   └── config/            # الإعدادات
├── requirements.txt       # المتطلبات
└── README.md             # هذا الملف
```

## 🤝 المساهمة
نرحب بمساهماتكم! يرجى قراءة دليل المساهمة قبل إرسال طلبات السحب.

## 📄 الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 التواصل
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://masroof.app

---
صُنع بـ ❤️ للمجتمع العربي
