"""
محلل البيانات المالية بالذكاء الصناعي
"""
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Tuple, Optional
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')

from ..config.settings import AI_CONFIG, CURRENCY_CONFIG

class ExpenseAnalyzer:
    """محلل المصروفات بالذكاء الصناعي"""
    
    def __init__(self):
        """تهيئة محلل المصروفات"""
        self.logger = logging.getLogger(__name__)
        self.min_data_points = AI_CONFIG['min_data_points']
        self.confidence_threshold = AI_CONFIG['confidence_threshold']
    
    def analyze_spending_patterns(self, transactions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل أنماط الإنفاق"""
        try:
            if len(transactions) < self.min_data_points:
                return {
                    'status': 'insufficient_data',
                    'message': f'يحتاج التحليل إلى {self.min_data_points} معاملة على الأقل',
                    'data_points': len(transactions)
                }
            
            df = pd.DataFrame(transactions)
            
            # تحويل التواريخ
            df['date'] = pd.to_datetime(df['date'])
            df['month'] = df['date'].dt.month
            df['day_of_week'] = df['date'].dt.dayofweek
            df['hour'] = df['date'].dt.hour
            
            # فلترة المصروفات فقط
            expenses = df[df['type'] == 'expense'].copy()
            
            if len(expenses) == 0:
                return {
                    'status': 'no_expenses',
                    'message': 'لا توجد مصروفات للتحليل'
                }
            
            analysis = {
                'status': 'success',
                'total_transactions': len(expenses),
                'analysis_period': {
                    'start_date': expenses['date'].min().strftime('%Y-%m-%d'),
                    'end_date': expenses['date'].max().strftime('%Y-%m-%d')
                },
                'spending_by_category': self._analyze_category_spending(expenses),
                'temporal_patterns': self._analyze_temporal_patterns(expenses),
                'spending_frequency': self._analyze_spending_frequency(expenses),
                'amount_patterns': self._analyze_amount_patterns(expenses),
                'insights': self._generate_spending_insights(expenses)
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل أنماط الإنفاق: {e}")
            return {
                'status': 'error',
                'message': f'حدث خطأ في التحليل: {str(e)}'
            }
    
    def _analyze_category_spending(self, expenses: pd.DataFrame) -> Dict[str, Any]:
        """تحليل الإنفاق حسب الفئات"""
        try:
            category_stats = expenses.groupby('category_id').agg({
                'amount': ['sum', 'mean', 'count', 'std']
            }).round(2)
            
            category_stats.columns = ['total', 'average', 'count', 'std']
            category_stats = category_stats.fillna(0)
            
            # حساب النسب المئوية
            total_spending = category_stats['total'].sum()
            category_stats['percentage'] = (category_stats['total'] / total_spending * 100).round(1)
            
            # ترتيب حسب الإجمالي
            category_stats = category_stats.sort_values('total', ascending=False)
            
            return {
                'top_categories': category_stats.head(5).to_dict('index'),
                'total_categories': len(category_stats),
                'most_expensive_category': category_stats.index[0] if len(category_stats) > 0 else None,
                'most_frequent_category': category_stats.sort_values('count', ascending=False).index[0] if len(category_stats) > 0 else None
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الإنفاق حسب الفئات: {e}")
            return {}
    
    def _analyze_temporal_patterns(self, expenses: pd.DataFrame) -> Dict[str, Any]:
        """تحليل الأنماط الزمنية للإنفاق"""
        try:
            patterns = {}
            
            # تحليل الإنفاق حسب الشهر
            monthly_spending = expenses.groupby('month')['amount'].agg(['sum', 'mean', 'count'])
            patterns['monthly'] = {
                'highest_month': int(monthly_spending['sum'].idxmax()),
                'lowest_month': int(monthly_spending['sum'].idxmin()),
                'average_monthly': float(monthly_spending['sum'].mean()),
                'data': monthly_spending.to_dict('index')
            }
            
            # تحليل الإنفاق حسب يوم الأسبوع
            daily_spending = expenses.groupby('day_of_week')['amount'].agg(['sum', 'mean', 'count'])
            day_names = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد']
            patterns['weekly'] = {
                'highest_day': day_names[daily_spending['sum'].idxmax()],
                'lowest_day': day_names[daily_spending['sum'].idxmin()],
                'weekend_vs_weekday': self._compare_weekend_weekday(expenses),
                'data': {day_names[i]: daily_spending.loc[i].to_dict() for i in daily_spending.index}
            }
            
            # تحليل الإنفاق حسب الساعة (إذا توفرت البيانات)
            if 'hour' in expenses.columns and expenses['hour'].notna().any():
                hourly_spending = expenses.groupby('hour')['amount'].agg(['sum', 'mean', 'count'])
                patterns['hourly'] = {
                    'peak_hour': int(hourly_spending['sum'].idxmax()),
                    'lowest_hour': int(hourly_spending['sum'].idxmin()),
                    'data': hourly_spending.to_dict('index')
                }
            
            return patterns
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل الأنماط الزمنية: {e}")
            return {}
    
    def _analyze_spending_frequency(self, expenses: pd.DataFrame) -> Dict[str, Any]:
        """تحليل تكرار الإنفاق"""
        try:
            # حساب المتوسط اليومي
            date_range = (expenses['date'].max() - expenses['date'].min()).days + 1
            daily_average = len(expenses) / date_range if date_range > 0 else 0
            
            # تحليل فترات الإنفاق
            expenses_sorted = expenses.sort_values('date')
            time_diffs = expenses_sorted['date'].diff().dt.days.dropna()
            
            frequency_analysis = {
                'total_transactions': len(expenses),
                'analysis_period_days': date_range,
                'daily_average_transactions': round(daily_average, 2),
                'average_days_between_transactions': round(time_diffs.mean(), 1) if len(time_diffs) > 0 else 0,
                'most_active_days': self._find_most_active_days(expenses),
                'spending_streaks': self._analyze_spending_streaks(expenses)
            }
            
            return frequency_analysis
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل تكرار الإنفاق: {e}")
            return {}
    
    def _analyze_amount_patterns(self, expenses: pd.DataFrame) -> Dict[str, Any]:
        """تحليل أنماط المبالغ"""
        try:
            amounts = expenses['amount']
            
            # إحصائيات أساسية
            stats = {
                'mean': float(amounts.mean()),
                'median': float(amounts.median()),
                'std': float(amounts.std()),
                'min': float(amounts.min()),
                'max': float(amounts.max()),
                'q25': float(amounts.quantile(0.25)),
                'q75': float(amounts.quantile(0.75))
            }
            
            # تصنيف المبالغ
            small_threshold = stats['q25']
            large_threshold = stats['q75']
            
            small_amounts = amounts[amounts <= small_threshold]
            medium_amounts = amounts[(amounts > small_threshold) & (amounts <= large_threshold)]
            large_amounts = amounts[amounts > large_threshold]
            
            amount_patterns = {
                'statistics': stats,
                'distribution': {
                    'small_amounts': {
                        'count': len(small_amounts),
                        'percentage': round(len(small_amounts) / len(amounts) * 100, 1),
                        'total': float(small_amounts.sum())
                    },
                    'medium_amounts': {
                        'count': len(medium_amounts),
                        'percentage': round(len(medium_amounts) / len(amounts) * 100, 1),
                        'total': float(medium_amounts.sum())
                    },
                    'large_amounts': {
                        'count': len(large_amounts),
                        'percentage': round(len(large_amounts) / len(amounts) * 100, 1),
                        'total': float(large_amounts.sum())
                    }
                },
                'outliers': self._detect_amount_outliers(amounts)
            }
            
            return amount_patterns
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل أنماط المبالغ: {e}")
            return {}
    
    def _generate_spending_insights(self, expenses: pd.DataFrame) -> List[Dict[str, str]]:
        """توليد رؤى ذكية حول الإنفاق"""
        try:
            insights = []
            
            # رؤى حول الفئات
            category_spending = expenses.groupby('category_id')['amount'].sum().sort_values(ascending=False)
            if len(category_spending) > 0:
                top_category = category_spending.index[0]
                top_percentage = (category_spending.iloc[0] / category_spending.sum() * 100)
                
                if top_percentage > 40:
                    insights.append({
                        'type': 'warning',
                        'title': 'تركز الإنفاق في فئة واحدة',
                        'description': f'أكثر من {top_percentage:.0f}% من إنفاقك في فئة واحدة. قد تحتاج لتنويع مصروفاتك.'
                    })
            
            # رؤى حول الأنماط الزمنية
            weekend_spending = expenses[expenses['day_of_week'].isin([5, 6])]['amount'].sum()
            weekday_spending = expenses[~expenses['day_of_week'].isin([5, 6])]['amount'].sum()
            
            if weekend_spending > weekday_spending * 0.5:
                insights.append({
                    'type': 'info',
                    'title': 'إنفاق عالي في عطلة نهاية الأسبوع',
                    'description': 'تنفق مبالغ كبيرة في عطلة نهاية الأسبوع. فكر في وضع ميزانية للترفيه.'
                })
            
            # رؤى حول المبالغ
            large_transactions = expenses[expenses['amount'] > expenses['amount'].quantile(0.9)]
            if len(large_transactions) > 0:
                insights.append({
                    'type': 'tip',
                    'title': 'مراجعة المصروفات الكبيرة',
                    'description': f'لديك {len(large_transactions)} معاملة كبيرة. تأكد من أنها ضرورية ومخططة.'
                })
            
            return insights
            
        except Exception as e:
            self.logger.error(f"خطأ في توليد الرؤى: {e}")
            return []
    
    def _compare_weekend_weekday(self, expenses: pd.DataFrame) -> Dict[str, float]:
        """مقارنة الإنفاق بين عطلة نهاية الأسبوع وأيام العمل"""
        try:
            weekend = expenses[expenses['day_of_week'].isin([5, 6])]
            weekday = expenses[~expenses['day_of_week'].isin([5, 6])]
            
            return {
                'weekend_total': float(weekend['amount'].sum()),
                'weekday_total': float(weekday['amount'].sum()),
                'weekend_average': float(weekend['amount'].mean()) if len(weekend) > 0 else 0,
                'weekday_average': float(weekday['amount'].mean()) if len(weekday) > 0 else 0,
                'weekend_count': len(weekend),
                'weekday_count': len(weekday)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في مقارنة عطلة نهاية الأسبوع: {e}")
            return {}
    
    def _find_most_active_days(self, expenses: pd.DataFrame) -> List[str]:
        """العثور على أكثر الأيام نشاطاً في الإنفاق"""
        try:
            daily_counts = expenses['date'].dt.date.value_counts().head(5)
            return [date.strftime('%Y-%m-%d') for date in daily_counts.index]
            
        except Exception as e:
            self.logger.error(f"خطأ في العثور على أكثر الأيام نشاطاً: {e}")
            return []
    
    def _analyze_spending_streaks(self, expenses: pd.DataFrame) -> Dict[str, Any]:
        """تحليل فترات الإنفاق المتتالية"""
        try:
            # تجميع المعاملات حسب التاريخ
            daily_expenses = expenses.groupby(expenses['date'].dt.date).size()
            dates = pd.date_range(daily_expenses.index.min(), daily_expenses.index.max(), freq='D')
            
            # إنشاء سلسلة كاملة من التواريخ
            full_series = pd.Series(0, index=dates.date)
            full_series.update(daily_expenses)
            
            # العثور على أطول فترة إنفاق متتالية
            spending_days = (full_series > 0).astype(int)
            streaks = []
            current_streak = 0
            
            for has_spending in spending_days:
                if has_spending:
                    current_streak += 1
                else:
                    if current_streak > 0:
                        streaks.append(current_streak)
                    current_streak = 0
            
            if current_streak > 0:
                streaks.append(current_streak)
            
            return {
                'longest_streak': max(streaks) if streaks else 0,
                'average_streak': np.mean(streaks) if streaks else 0,
                'total_streaks': len(streaks)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل فترات الإنفاق: {e}")
            return {}
    
    def _detect_amount_outliers(self, amounts: pd.Series) -> Dict[str, Any]:
        """اكتشاف القيم الشاذة في المبالغ"""
        try:
            Q1 = amounts.quantile(0.25)
            Q3 = amounts.quantile(0.75)
            IQR = Q3 - Q1
            
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = amounts[(amounts < lower_bound) | (amounts > upper_bound)]
            
            return {
                'count': len(outliers),
                'percentage': round(len(outliers) / len(amounts) * 100, 1),
                'values': outliers.tolist() if len(outliers) <= 10 else outliers.head(10).tolist(),
                'bounds': {
                    'lower': float(lower_bound),
                    'upper': float(upper_bound)
                }
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في اكتشاف القيم الشاذة: {e}")
            return {}

# مثيل عام لمحلل المصروفات
expense_analyzer = ExpenseAnalyzer()
