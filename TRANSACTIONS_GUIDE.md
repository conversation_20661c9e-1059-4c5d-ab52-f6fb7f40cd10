# 💳 دليل نافذة إدارة المعاملات

## 🎯 نظرة عامة
نافذة إدارة المعاملات هي القلب النابض لبرنامج مصروف، حيث يمكنك تسجيل وإدارة جميع معاملاتك المالية من دخل ومصروفات بطريقة سهلة ومنظمة.

## 🏗️ مكونات النافذة

### 📋 الشريط الجانبي (لوحة التحكم)
يحتوي على جميع الأدوات اللازمة لإدارة المعاملات:

#### 🔄 اختيار نوع المعاملة
- **📈 دخل**: لتسجيل جميع مصادر الدخل
- **📉 مصروف**: لتسجيل جميع المصروفات

#### 📝 نموذج إدخال المعاملة
- **💰 المبلغ**: قيمة المعاملة بالدينار العراقي
- **📝 الوصف**: وصف مختصر للمعاملة
- **📂 الفئة**: تصنيف المعاملة (يتغير حسب النوع)
- **💳 طريقة الدفع**: نقدي، بطاقة، تحويل بنكي، محفظة رقمية
- **📅 التاريخ**: تاريخ المعاملة (افتراضياً اليوم)
- **📋 ملاحظات**: تفاصيل إضافية اختيارية

#### ⚡ أزرار العمليات
- **➕ إضافة معاملة**: لحفظ معاملة جديدة
- **✏️ تحديث معاملة**: لتعديل معاملة موجودة
- **🗑️ حذف معاملة**: لحذف معاملة محددة
- **🔄 مسح النموذج**: لمسح جميع الحقول

### 📊 المنطقة الرئيسية

#### 🔍 شريط البحث والفلترة
- **البحث**: للبحث في الوصف، المبلغ، أو التاريخ
- **الفلتر**: لعرض الكل، الدخل فقط، أو المصروفات فقط
- **🔄 تحديث**: لإعادة تحميل البيانات

#### 📋 جدول المعاملات
يعرض جميع المعاملات مع الأعمدة التالية:
- **النوع**: 📈 دخل أو 📉 مصروف
- **المبلغ**: القيمة بالدينار العراقي
- **الوصف**: وصف المعاملة
- **الفئة**: تصنيف المعاملة
- **طريقة الدفع**: وسيلة الدفع المستخدمة
- **التاريخ**: تاريخ المعاملة

#### 📈 شريط الإحصائيات
يعرض ملخص سريع:
- **📈 إجمالي الدخل**: مجموع جميع المداخيل
- **📉 إجمالي المصروفات**: مجموع جميع المصروفات
- **💰 الرصيد**: الفرق بين الدخل والمصروفات (ملون حسب القيمة)
- **📊 عدد المعاملات**: العدد الإجمالي للمعاملات

## 🎯 فئات الدخل المتاحة

### 💼 مصادر الدخل الأساسية
- **راتب أساسي**: الراتب من الوظيفة الرئيسية
- **راتب إضافي**: دخل من وظيفة ثانوية أو عمل جانبي
- **مكافآت**: مكافآت الأداء والحوافز
- **استثمارات**: أرباح من الاستثمارات والأسهم
- **أعمال جانبية**: دخل من مشاريع شخصية
- **هدايا**: هدايا نقدية
- **أخرى**: أي مصدر دخل آخر

### 🛍️ فئات المصروفات المتاحة
- **طعام وشراب**: وجبات ومشروبات
- **مواصلات**: نقل وسفر
- **سكن وإيجار**: إيجار ومصاريف السكن
- **فواتير**: كهرباء، ماء، إنترنت
- **صحة وطب**: أدوية وعلاج
- **تعليم**: رسوم دراسية وكتب
- **ترفيه**: سينما ومطاعم
- **ملابس**: ملابس وأحذية
- **تسوق**: مشتريات عامة
- **وقود**: وقود السيارة
- **صيانة**: صيانة وإصلاحات
- **أخرى**: مصروفات أخرى

## 🔧 كيفية الاستخدام

### ➕ إضافة معاملة جديدة
1. اختر نوع المعاملة (دخل أو مصروف)
2. أدخل المبلغ بالأرقام فقط
3. اكتب وصف واضح للمعاملة
4. اختر الفئة المناسبة
5. حدد طريقة الدفع
6. تأكد من التاريخ (أو عدله حسب الحاجة)
7. أضف ملاحظات إضافية إذا لزم الأمر
8. اضغط "➕ إضافة معاملة"

### ✏️ تعديل معاملة موجودة
1. اضغط على المعاملة في الجدول لتحديدها
2. ستظهر بياناتها في النموذج تلقائياً
3. عدل البيانات المطلوبة
4. اضغط "✏️ تحديث معاملة"

### 🗑️ حذف معاملة
1. اضغط على المعاملة في الجدول لتحديدها
2. اضغط "🗑️ حذف معاملة"
3. أكد الحذف في النافذة المنبثقة

### 🔍 البحث والفلترة
- **للبحث**: اكتب في مربع البحث أي كلمة من الوصف أو جزء من المبلغ أو التاريخ
- **للفلترة**: اختر من قائمة الفلتر (الكل، دخل، مصروف)
- **للتحديث**: اضغط زر التحديث لإعادة تحميل جميع البيانات

## 💡 نصائح للاستخدام الأمثل

### 📝 كتابة الوصف
- استخدم أوصاف واضحة ومفهومة
- أضف تفاصيل مهمة (مثل: "تسوق - سوبر ماركت الأهلي")
- تجنب الاختصارات غير المفهومة

### 💰 إدخال المبالغ
- أدخل المبلغ بالأرقام فقط (بدون فواصل أو رموز)
- مثال: 150000 بدلاً من 150,000 د.ع
- البرنامج سيضيف التنسيق تلقائياً

### 📅 التواريخ
- استخدم تنسيق YYYY-MM-DD (مثل: 2025-05-28)
- يمكنك تعديل التاريخ لتسجيل معاملات سابقة
- التاريخ الافتراضي هو اليوم الحالي

### 🏷️ الفئات
- اختر الفئة الأنسب لكل معاملة
- هذا يساعد في التحليلات والتقارير لاحقاً
- يمكن استخدام "أخرى" للمعاملات غير المصنفة

### 📋 الملاحظات
- استخدم الملاحظات للتفاصيل الإضافية
- مفيدة لتذكر سبب المعاملة أو ظروفها
- يمكن تركها فارغة إذا لم تكن ضرورية

## 🎨 الألوان والرموز

### 🎯 رموز أنواع المعاملات
- **📈**: دخل (أخضر)
- **📉**: مصروف (أحمر)

### 💰 ألوان الرصيد
- **أخضر**: رصيد موجب (فائض)
- **أحمر**: رصيد سالب (عجز)

### 🔘 حالة الأزرار
- **مفعل**: يمكن الضغط عليه
- **معطل**: غير متاح (رمادي)

## ⚠️ رسائل التحذير والأخطاء

### ✅ رسائل النجاح
- "تم إضافة المعاملة بنجاح!"
- "تم تحديث المعاملة بنجاح!"
- "تم حذف المعاملة بنجاح!"

### ❌ رسائل الخطأ الشائعة
- "يرجى إدخال المبلغ" - لم تدخل مبلغ
- "يجب أن يكون المبلغ أكبر من صفر" - مبلغ سالب أو صفر
- "يرجى إدخال مبلغ صحيح" - مبلغ غير رقمي
- "يرجى إدخال وصف للمعاملة" - وصف فارغ
- "تنسيق التاريخ غير صحيح" - تاريخ بتنسيق خاطئ

## 🔄 اختصارات لوحة المفاتيح (مستقبلية)
- **Ctrl+N**: إضافة معاملة جديدة
- **Ctrl+S**: حفظ المعاملة
- **Delete**: حذف المعاملة المحددة
- **F5**: تحديث البيانات
- **Ctrl+F**: التركيز على مربع البحث

## 📊 الإحصائيات المعروضة

### 📈 إجمالي الدخل
- مجموع جميع المعاملات من نوع "دخل"
- يتم تحديثه تلقائياً عند إضافة أو تعديل معاملات

### 📉 إجمالي المصروفات  
- مجموع جميع المعاملات من نوع "مصروف"
- يتم تحديثه تلقائياً عند إضافة أو تعديل معاملات

### 💰 الرصيد
- الفرق بين إجمالي الدخل وإجمالي المصروفات
- **موجب**: لديك فائض مالي
- **سالب**: لديك عجز مالي
- **صفر**: رصيدك متوازن

### 📊 عدد المعاملات
- العدد الإجمالي لجميع المعاملات المسجلة
- يشمل الدخل والمصروفات

---

## 🎉 مثال عملي

### سيناريو: تسجيل راتب شهري
1. اختر "📈 دخل"
2. أدخل المبلغ: 2500000
3. الوصف: "راتب شهر مايو 2025"
4. الفئة: "راتب أساسي"
5. طريقة الدفع: "تحويل بنكي"
6. التاريخ: 2025-05-01
7. الملاحظات: "راتب الوظيفة الحكومية"
8. اضغط "➕ إضافة معاملة"

### سيناريو: تسجيل مصروف البقالة
1. اختر "📉 مصروف"
2. أدخل المبلغ: 150000
3. الوصف: "تسوق أسبوعي - بقالة الحي"
4. الفئة: "طعام وشراب"
5. طريقة الدفع: "نقدي"
6. التاريخ: 2025-05-28
7. الملاحظات: "خضار وفواكه ومواد أساسية"
8. اضغط "➕ إضافة معاملة"

---

**💡 نصيحة**: استخدم نافذة المعاملات بانتظام لتتبع وضعك المالي بدقة، وستحصل على تحليلات وتقارير مفيدة من الذكاء الصناعي في البرنامج!
