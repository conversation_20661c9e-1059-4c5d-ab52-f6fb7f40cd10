"""
النافذة الرئيسية للبرنامج
"""
import customtkinter as ctk
from tkinter import messagebox
import logging
from datetime import datetime, date
from typing import Optional

from ..config.settings import UI_CONFIG, MESSAGES, CURRENCY_CONFIG
from ..database.database import DatabaseManager

class MainWindow(ctk.CTk):
    """النافذة الرئيسية لبرنامج مصروف"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # إعداد النافذة
        self.setup_window()
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
    
    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.title("مصروف - إدارة المصروفات الشخصية")
        self.geometry(f"{UI_CONFIG['window_size'][0]}x{UI_CONFIG['window_size'][1]}")
        self.minsize(UI_CONFIG['window_min_size'][0], UI_CONFIG['window_min_size'][1])
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد الشبكة
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.update_idletasks()
        width = self.winfo_width()
        height = self.winfo_height()
        x = (self.winfo_screenwidth() // 2) - (width // 2)
        y = (self.winfo_screenheight() // 2) - (height // 2)
        self.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط الجانبي
        self.create_sidebar()
        
        # المنطقة الرئيسية
        self.create_main_area()
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        self.sidebar_frame = ctk.CTkFrame(self, width=250, corner_radius=0)
        self.sidebar_frame.grid(row=0, column=0, rowspan=4, sticky="nsew")
        self.sidebar_frame.grid_rowconfigure(10, weight=1)
        
        # شعار البرنامج
        self.logo_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="💰 مصروف",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.logo_label.grid(row=0, column=0, padx=20, pady=(20, 10))
        
        # أزرار التنقل
        self.nav_buttons = {}
        
        # زر لوحة التحكم
        self.nav_buttons['dashboard'] = ctk.CTkButton(
            self.sidebar_frame,
            text="📊 لوحة التحكم",
            command=self.show_dashboard,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.nav_buttons['dashboard'].grid(row=1, column=0, padx=20, pady=10, sticky="ew")
        
        # زر المعاملات
        self.nav_buttons['transactions'] = ctk.CTkButton(
            self.sidebar_frame,
            text="💳 المعاملات",
            command=self.show_transactions,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.nav_buttons['transactions'].grid(row=2, column=0, padx=20, pady=10, sticky="ew")
        
        # زر الديون
        self.nav_buttons['debts'] = ctk.CTkButton(
            self.sidebar_frame,
            text="💸 الديون",
            command=self.show_debts,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.nav_buttons['debts'].grid(row=3, column=0, padx=20, pady=10, sticky="ew")
        
        # زر الميزانيات
        self.nav_buttons['budgets'] = ctk.CTkButton(
            self.sidebar_frame,
            text="📋 الميزانيات",
            command=self.show_budgets,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.nav_buttons['budgets'].grid(row=4, column=0, padx=20, pady=10, sticky="ew")
        
        # زر التقارير
        self.nav_buttons['reports'] = ctk.CTkButton(
            self.sidebar_frame,
            text="📈 التقارير",
            command=self.show_reports,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.nav_buttons['reports'].grid(row=5, column=0, padx=20, pady=10, sticky="ew")
        
        # زر الإعدادات
        self.nav_buttons['settings'] = ctk.CTkButton(
            self.sidebar_frame,
            text="⚙️ الإعدادات",
            command=self.show_settings,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.nav_buttons['settings'].grid(row=6, column=0, padx=20, pady=10, sticky="ew")
        
        # معلومات المستخدم
        self.user_frame = ctk.CTkFrame(self.sidebar_frame)
        self.user_frame.grid(row=11, column=0, padx=20, pady=20, sticky="ew")
        
        self.user_label = ctk.CTkLabel(
            self.user_frame,
            text="👤 مستخدم",
            font=ctk.CTkFont(size=12)
        )
        self.user_label.pack(pady=10)
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, padx=20, pady=20, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(0, weight=1)
        
        # إطار المحتوى
        self.content_frame = ctk.CTkFrame(self.main_frame)
        self.content_frame.grid(row=0, column=0, padx=20, pady=20, sticky="nsew")
        self.content_frame.grid_columnconfigure(0, weight=1)
        self.content_frame.grid_rowconfigure(0, weight=1)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_frame = ctk.CTkFrame(self, height=30)
        self.status_frame.grid(row=1, column=1, padx=20, pady=(0, 20), sticky="ew")
        
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="جاهز",
            font=ctk.CTkFont(size=10)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # عرض التاريخ والوقت
        self.datetime_label = ctk.CTkLabel(
            self.status_frame,
            text=datetime.now().strftime("%Y-%m-%d %H:%M"),
            font=ctk.CTkFont(size=10)
        )
        self.datetime_label.pack(side="right", padx=10, pady=5)
    
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_content()
        self.update_nav_buttons('dashboard')
        
        # عنوان الصفحة
        title_label = ctk.CTkLabel(
            self.content_frame,
            text="📊 لوحة التحكم",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.grid(row=0, column=0, pady=20, sticky="w")
        
        # بطاقات الملخص
        self.create_summary_cards()
        
        # الرسوم البيانية
        self.create_charts_section()
    
    def create_summary_cards(self):
        """إنشاء بطاقات الملخص"""
        cards_frame = ctk.CTkFrame(self.content_frame)
        cards_frame.grid(row=1, column=0, pady=20, sticky="ew")
        cards_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # بطاقة الرصيد الإجمالي
        balance_card = self.create_summary_card(
            cards_frame, "💰", "الرصيد الإجمالي", "0 د.ع", 0, 0
        )
        
        # بطاقة الدخل الشهري
        income_card = self.create_summary_card(
            cards_frame, "📈", "الدخل الشهري", "0 د.ع", 0, 1
        )
        
        # بطاقة المصروفات الشهرية
        expense_card = self.create_summary_card(
            cards_frame, "📉", "المصروفات الشهرية", "0 د.ع", 0, 2
        )
        
        # بطاقة الديون
        debt_card = self.create_summary_card(
            cards_frame, "💸", "إجمالي الديون", "0 د.ع", 0, 3
        )
    
    def create_summary_card(self, parent, icon, title, value, row, col):
        """إنشاء بطاقة ملخص"""
        card = ctk.CTkFrame(parent)
        card.grid(row=row, column=col, padx=10, pady=10, sticky="ew")
        
        icon_label = ctk.CTkLabel(card, text=icon, font=ctk.CTkFont(size=24))
        icon_label.pack(pady=(10, 5))
        
        title_label = ctk.CTkLabel(card, text=title, font=ctk.CTkFont(size=12))
        title_label.pack()
        
        value_label = ctk.CTkLabel(card, text=value, font=ctk.CTkFont(size=16, weight="bold"))
        value_label.pack(pady=(5, 10))
        
        return card
    
    def create_charts_section(self):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = ctk.CTkFrame(self.content_frame)
        charts_frame.grid(row=2, column=0, pady=20, sticky="nsew")
        charts_frame.grid_columnconfigure((0, 1), weight=1)
        charts_frame.grid_rowconfigure(0, weight=1)
        
        # رسم بياني للمصروفات
        expense_chart_frame = ctk.CTkFrame(charts_frame)
        expense_chart_frame.grid(row=0, column=0, padx=10, pady=10, sticky="nsew")
        
        expense_title = ctk.CTkLabel(
            expense_chart_frame,
            text="📊 توزيع المصروفات",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        expense_title.pack(pady=10)
        
        # رسم بياني للدخل
        income_chart_frame = ctk.CTkFrame(charts_frame)
        income_chart_frame.grid(row=0, column=1, padx=10, pady=10, sticky="nsew")
        
        income_title = ctk.CTkLabel(
            income_chart_frame,
            text="📈 تطور الدخل",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        income_title.pack(pady=10)
    
    def update_nav_buttons(self, active_button):
        """تحديث حالة أزرار التنقل"""
        for name, button in self.nav_buttons.items():
            if name == active_button:
                button.configure(fg_color=("gray75", "gray25"))
            else:
                button.configure(fg_color=("gray85", "gray15"))
    
    def show_transactions(self):
        """عرض صفحة المعاملات"""
        self.clear_content()
        self.update_nav_buttons('transactions')

        # استيراد نافذة المعاملات
        from .transactions_window import TransactionsWindow

        # إنشاء نافذة المعاملات داخل المحتوى
        self.transactions_window = TransactionsWindow(self.content_frame, self.db_manager)
        self.transactions_window.pack(fill="both", expand=True)
    
    def show_debts(self):
        """عرض صفحة الديون"""
        self.clear_content()
        self.update_nav_buttons('debts')
        
        title_label = ctk.CTkLabel(
            self.content_frame,
            text="💸 الديون",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
    
    def show_budgets(self):
        """عرض صفحة الميزانيات"""
        self.clear_content()
        self.update_nav_buttons('budgets')
        
        title_label = ctk.CTkLabel(
            self.content_frame,
            text="📋 الميزانيات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
    
    def show_reports(self):
        """عرض صفحة التقارير"""
        self.clear_content()
        self.update_nav_buttons('reports')
        
        title_label = ctk.CTkLabel(
            self.content_frame,
            text="📈 التقارير",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
    
    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.clear_content()
        self.update_nav_buttons('settings')
        
        title_label = ctk.CTkLabel(
            self.content_frame,
            text="⚙️ الإعدادات",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
    
    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحديث شريط الحالة
            self.status_label.configure(text="تم تحميل البيانات بنجاح")
            self.logger.info("تم تحميل البيانات الأولية")
        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {e}")
    
    def update_status(self, message: str):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=message)
        self.datetime_label.configure(text=datetime.now().strftime("%Y-%m-%d %H:%M"))
