"""
نافذة إدارة المعاملات (الدخل والمصروفات)
"""
import customtkinter as ctk
from tkinter import messagebox, ttk
import logging
from datetime import datetime, date
from typing import Optional, List, Dict, Any

from ..config.settings import UI_CONFIG, CURRENCY_CONFIG, DEFAULT_INCOME_CATEGORIES, DEFAULT_EXPENSE_CATEGORIES
from ..database.models import Transaction, TransactionType, PaymentMethod, Category
from ..database.database import DatabaseManager

class TransactionsWindow(ctk.CTkFrame):
    """نافذة إدارة المعاملات"""
    
    def __init__(self, parent, db_manager: DatabaseManager):
        super().__init__(parent)
        
        self.db_manager = db_manager
        self.logger = logging.getLogger(__name__)
        
        # متغيرات الواجهة
        self.current_transaction = None
        self.transactions_data = []
        
        # إعداد الشبكة
        self.grid_columnconfigure(1, weight=1)
        self.grid_rowconfigure(0, weight=1)
        
        # إنشاء الواجهة
        self.create_widgets()
        
        # تحميل البيانات الأولية
        self.load_initial_data()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الشريط الجانبي للتحكم
        self.create_sidebar()
        
        # المنطقة الرئيسية
        self.create_main_area()
    
    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        self.sidebar_frame = ctk.CTkFrame(self, width=300)
        self.sidebar_frame.grid(row=0, column=0, padx=(0, 10), pady=0, sticky="nsew")
        self.sidebar_frame.grid_propagate(False)
        
        # عنوان القسم
        title_label = ctk.CTkLabel(
            self.sidebar_frame,
            text="💳 إدارة المعاملات",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # أزرار التبديل بين الدخل والمصروفات
        self.transaction_type_var = ctk.StringVar(value="expense")  # البدء بالمصروفات

        type_frame = ctk.CTkFrame(self.sidebar_frame)
        type_frame.pack(pady=10, padx=20, fill="x")

        # إضافة عنوان للقسم
        type_title = ctk.CTkLabel(
            type_frame,
            text="📊 نوع المعاملة",
            font=ctk.CTkFont(size=14, weight="bold")
        )
        type_title.pack(pady=(10, 5))

        income_radio = ctk.CTkRadioButton(
            type_frame,
            text="📈 دخل",
            variable=self.transaction_type_var,
            value="income",
            command=self.on_type_change,
            font=ctk.CTkFont(size=14)
        )
        income_radio.pack(pady=5, anchor="w", padx=10)

        expense_radio = ctk.CTkRadioButton(
            type_frame,
            text="📉 مصروف",
            variable=self.transaction_type_var,
            value="expense",
            command=self.on_type_change,
            font=ctk.CTkFont(size=14)
        )
        expense_radio.pack(pady=(5, 10), anchor="w", padx=10)
        
        # نموذج إضافة/تعديل المعاملة
        self.create_transaction_form()
        
        # أزرار العمليات
        self.create_action_buttons()
    
    def create_transaction_form(self):
        """إنشاء نموذج المعاملة"""
        form_frame = ctk.CTkFrame(self.sidebar_frame)
        form_frame.pack(pady=20, padx=20, fill="x")
        
        # المبلغ
        amount_label = ctk.CTkLabel(form_frame, text="💰 المبلغ (د.ع):")
        amount_label.pack(pady=(10, 5), anchor="w")
        
        self.amount_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="أدخل المبلغ",
            font=ctk.CTkFont(size=14)
        )
        self.amount_entry.pack(pady=(0, 10), fill="x")
        
        # الوصف
        description_label = ctk.CTkLabel(form_frame, text="📝 الوصف:")
        description_label.pack(pady=(0, 5), anchor="w")
        
        self.description_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="وصف المعاملة",
            font=ctk.CTkFont(size=14)
        )
        self.description_entry.pack(pady=(0, 10), fill="x")
        
        # الفئة
        category_label = ctk.CTkLabel(form_frame, text="📂 الفئة:")
        category_label.pack(pady=(0, 5), anchor="w")
        
        self.category_var = ctk.StringVar()
        self.category_combo = ctk.CTkComboBox(
            form_frame,
            variable=self.category_var,
            font=ctk.CTkFont(size=14),
            state="readonly"
        )
        self.category_combo.pack(pady=(0, 10), fill="x")
        
        # طريقة الدفع
        payment_label = ctk.CTkLabel(form_frame, text="💳 طريقة الدفع:")
        payment_label.pack(pady=(0, 5), anchor="w")
        
        self.payment_var = ctk.StringVar(value="cash")
        self.payment_combo = ctk.CTkComboBox(
            form_frame,
            variable=self.payment_var,
            values=["نقدي", "بطاقة", "تحويل بنكي", "محفظة رقمية"],
            font=ctk.CTkFont(size=14),
            state="readonly"
        )
        self.payment_combo.pack(pady=(0, 10), fill="x")
        
        # التاريخ
        date_label = ctk.CTkLabel(form_frame, text="📅 التاريخ:")
        date_label.pack(pady=(0, 5), anchor="w")
        
        self.date_entry = ctk.CTkEntry(
            form_frame,
            placeholder_text="YYYY-MM-DD",
            font=ctk.CTkFont(size=14)
        )
        self.date_entry.pack(pady=(0, 10), fill="x")
        
        # تعيين التاريخ الحالي
        self.date_entry.insert(0, date.today().strftime("%Y-%m-%d"))
        
        # الملاحظات
        notes_label = ctk.CTkLabel(form_frame, text="📋 ملاحظات:")
        notes_label.pack(pady=(0, 5), anchor="w")
        
        self.notes_entry = ctk.CTkTextbox(
            form_frame,
            height=60,
            font=ctk.CTkFont(size=12)
        )
        self.notes_entry.pack(pady=(0, 10), fill="x")
    
    def create_action_buttons(self):
        """إنشاء أزرار العمليات"""
        buttons_frame = ctk.CTkFrame(self.sidebar_frame)
        buttons_frame.pack(pady=20, padx=20, fill="x")
        
        # زر إضافة
        self.add_button = ctk.CTkButton(
            buttons_frame,
            text="➕ إضافة معاملة",
            command=self.add_transaction,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40
        )
        self.add_button.pack(pady=5, fill="x")
        
        # زر تحديث
        self.update_button = ctk.CTkButton(
            buttons_frame,
            text="✏️ تحديث معاملة",
            command=self.update_transaction,
            font=ctk.CTkFont(size=14),
            height=40,
            state="disabled"
        )
        self.update_button.pack(pady=5, fill="x")
        
        # زر حذف
        self.delete_button = ctk.CTkButton(
            buttons_frame,
            text="🗑️ حذف معاملة",
            command=self.delete_transaction,
            font=ctk.CTkFont(size=14),
            height=40,
            fg_color="red",
            hover_color="darkred",
            state="disabled"
        )
        self.delete_button.pack(pady=5, fill="x")
        
        # زر مسح النموذج
        clear_button = ctk.CTkButton(
            buttons_frame,
            text="🔄 مسح النموذج",
            command=self.clear_form,
            font=ctk.CTkFont(size=14),
            height=40
        )
        clear_button.pack(pady=5, fill="x")
    
    def create_main_area(self):
        """إنشاء المنطقة الرئيسية"""
        self.main_frame = ctk.CTkFrame(self)
        self.main_frame.grid(row=0, column=1, padx=0, pady=0, sticky="nsew")
        self.main_frame.grid_columnconfigure(0, weight=1)
        self.main_frame.grid_rowconfigure(1, weight=1)
        
        # شريط البحث والفلترة
        self.create_search_bar()
        
        # جدول المعاملات
        self.create_transactions_table()
        
        # شريط الإحصائيات
        self.create_stats_bar()
    
    def create_search_bar(self):
        """إنشاء شريط البحث والفلترة"""
        search_frame = ctk.CTkFrame(self.main_frame)
        search_frame.grid(row=0, column=0, pady=(0, 10), sticky="ew")
        search_frame.grid_columnconfigure(1, weight=1)
        
        # البحث
        search_label = ctk.CTkLabel(search_frame, text="🔍")
        search_label.grid(row=0, column=0, padx=(10, 5), pady=10)
        
        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="البحث في المعاملات...",
            font=ctk.CTkFont(size=14)
        )
        self.search_entry.grid(row=0, column=1, padx=5, pady=10, sticky="ew")
        self.search_entry.bind("<KeyRelease>", self.on_search)
        
        # فلتر النوع
        filter_label = ctk.CTkLabel(search_frame, text="📂")
        filter_label.grid(row=0, column=2, padx=(10, 5), pady=10)
        
        self.filter_var = ctk.StringVar(value="الكل")
        self.filter_combo = ctk.CTkComboBox(
            search_frame,
            variable=self.filter_var,
            values=["الكل", "دخل", "مصروف"],
            command=self.on_filter_change,
            width=120
        )
        self.filter_combo.grid(row=0, column=3, padx=5, pady=10)
        
        # زر تحديث
        refresh_button = ctk.CTkButton(
            search_frame,
            text="🔄",
            command=self.refresh_data,
            width=40
        )
        refresh_button.grid(row=0, column=4, padx=(5, 10), pady=10)
    
    def create_transactions_table(self):
        """إنشاء جدول المعاملات"""
        # إطار الجدول
        table_frame = ctk.CTkFrame(self.main_frame)
        table_frame.grid(row=1, column=0, sticky="nsew")
        table_frame.grid_columnconfigure(0, weight=1)
        table_frame.grid_rowconfigure(0, weight=1)
        
        # إنشاء Treeview للجدول
        columns = ("النوع", "المبلغ", "الوصف", "الفئة", "طريقة الدفع", "التاريخ")
        
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor="center")
        
        # تخصيص عرض الأعمدة
        self.tree.column("النوع", width=80)
        self.tree.column("المبلغ", width=120)
        self.tree.column("الوصف", width=200)
        self.tree.column("الفئة", width=120)
        self.tree.column("طريقة الدفع", width=120)
        self.tree.column("التاريخ", width=100)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient="vertical", command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)
        
        # وضع الجدول وشريط التمرير
        self.tree.grid(row=0, column=0, sticky="nsew")
        scrollbar.grid(row=0, column=1, sticky="ns")
        
        # ربط الأحداث
        self.tree.bind("<<TreeviewSelect>>", self.on_transaction_select)
        self.tree.bind("<Double-1>", self.on_transaction_double_click)
    
    def create_stats_bar(self):
        """إنشاء شريط الإحصائيات"""
        stats_frame = ctk.CTkFrame(self.main_frame)
        stats_frame.grid(row=2, column=0, pady=(10, 0), sticky="ew")
        stats_frame.grid_columnconfigure((0, 1, 2, 3), weight=1)
        
        # إجمالي الدخل
        self.income_label = ctk.CTkLabel(
            stats_frame,
            text="📈 إجمالي الدخل: 0 د.ع",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.income_label.grid(row=0, column=0, padx=10, pady=10)
        
        # إجمالي المصروفات
        self.expense_label = ctk.CTkLabel(
            stats_frame,
            text="📉 إجمالي المصروفات: 0 د.ع",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.expense_label.grid(row=0, column=1, padx=10, pady=10)
        
        # الرصيد
        self.balance_label = ctk.CTkLabel(
            stats_frame,
            text="💰 الرصيد: 0 د.ع",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.balance_label.grid(row=0, column=2, padx=10, pady=10)
        
        # عدد المعاملات
        self.count_label = ctk.CTkLabel(
            stats_frame,
            text="📊 عدد المعاملات: 0",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.count_label.grid(row=0, column=3, padx=10, pady=10)

    def load_initial_data(self):
        """تحميل البيانات الأولية"""
        try:
            # تحميل الفئات
            self.load_categories()

            # تحميل المعاملات
            self.load_transactions()

            # تحديث الإحصائيات
            self.update_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل البيانات الأولية: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل البيانات: {e}")

    def load_categories(self):
        """تحميل الفئات"""
        try:
            # تحديث الفئات حسب نوع المعاملة
            self.on_type_change()

        except Exception as e:
            self.logger.error(f"خطأ في تحميل الفئات: {e}")

    def on_type_change(self):
        """تغيير نوع المعاملة"""
        try:
            transaction_type = self.transaction_type_var.get()

            if transaction_type == "income":
                # فئات الدخل - مع إضافة مصدرين للدخل كما طلبت
                categories = [
                    "راتب أساسي",
                    "راتب إضافي",
                    "مكافآت",
                    "استثمارات",
                    "أعمال جانبية",
                    "هدايا",
                    "أخرى"
                ]
            else:
                # فئات المصروفات
                categories = DEFAULT_EXPENSE_CATEGORIES

            self.category_combo.configure(values=categories)
            if categories:
                self.category_var.set(categories[0])

        except Exception as e:
            self.logger.error(f"خطأ في تغيير نوع المعاملة: {e}")

    def add_transaction(self):
        """إضافة معاملة جديدة"""
        try:
            # التحقق من صحة البيانات
            if not self.validate_form():
                return

            # جمع البيانات من النموذج
            transaction_data = self.get_form_data()

            # إدراج في قاعدة البيانات
            query = """
                INSERT INTO transactions (type, amount, description, category_id,
                                        payment_method, date, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """

            # تحويل نوع المعاملة
            trans_type = "income" if self.transaction_type_var.get() == "income" else "expense"

            # تحويل طريقة الدفع
            payment_methods = {
                "نقدي": "cash",
                "بطاقة": "card",
                "تحويل بنكي": "bank",
                "محفظة رقمية": "digital"
            }
            payment_method = payment_methods.get(transaction_data['payment_method'], "cash")

            params = (
                trans_type,
                transaction_data['amount'],
                transaction_data['description'],
                1,  # category_id مؤقت
                payment_method,
                transaction_data['date'],
                transaction_data['notes']
            )

            transaction_id = self.db_manager.execute_insert(query, params)

            if transaction_id:
                messagebox.showinfo("نجح", "تم إضافة المعاملة بنجاح!")
                self.clear_form()
                self.load_transactions()
                self.update_statistics()

                # إشعار بإضافة المعاملة
                from ..utils.notifications import notification_manager
                notification_manager.notify_transaction_added(
                    trans_type,
                    transaction_data['amount'],
                    transaction_data['category']
                )

        except Exception as e:
            self.logger.error(f"خطأ في إضافة المعاملة: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في إضافة المعاملة: {e}")

    def update_transaction(self):
        """تحديث معاملة موجودة"""
        try:
            if not self.current_transaction:
                messagebox.showwarning("تحذير", "يرجى اختيار معاملة للتحديث")
                return

            if not self.validate_form():
                return

            transaction_data = self.get_form_data()

            query = """
                UPDATE transactions
                SET type=?, amount=?, description=?, payment_method=?, date=?, notes=?
                WHERE id=?
            """

            trans_type = "income" if self.transaction_type_var.get() == "income" else "expense"

            payment_methods = {
                "نقدي": "cash",
                "بطاقة": "card",
                "تحويل بنكي": "bank",
                "محفظة رقمية": "digital"
            }
            payment_method = payment_methods.get(transaction_data['payment_method'], "cash")

            params = (
                trans_type,
                transaction_data['amount'],
                transaction_data['description'],
                payment_method,
                transaction_data['date'],
                transaction_data['notes'],
                self.current_transaction
            )

            rows_affected = self.db_manager.execute_update(query, params)

            if rows_affected > 0:
                messagebox.showinfo("نجح", "تم تحديث المعاملة بنجاح!")
                self.clear_form()
                self.load_transactions()
                self.update_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في تحديث المعاملة: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تحديث المعاملة: {e}")

    def delete_transaction(self):
        """حذف معاملة"""
        try:
            if not self.current_transaction:
                messagebox.showwarning("تحذير", "يرجى اختيار معاملة للحذف")
                return

            # تأكيد الحذف
            result = messagebox.askyesno(
                "تأكيد الحذف",
                "هل أنت متأكد من حذف هذه المعاملة؟\nلا يمكن التراجع عن هذا الإجراء."
            )

            if result:
                query = "DELETE FROM transactions WHERE id=?"
                rows_affected = self.db_manager.execute_update(query, (self.current_transaction,))

                if rows_affected > 0:
                    messagebox.showinfo("نجح", "تم حذف المعاملة بنجاح!")
                    self.clear_form()
                    self.load_transactions()
                    self.update_statistics()

        except Exception as e:
            self.logger.error(f"خطأ في حذف المعاملة: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في حذف المعاملة: {e}")

    def validate_form(self):
        """التحقق من صحة بيانات النموذج"""
        try:
            # التحقق من المبلغ
            amount_text = self.amount_entry.get().strip()
            if not amount_text:
                messagebox.showerror("خطأ", "يرجى إدخال المبلغ")
                return False

            try:
                amount = float(amount_text)
                if amount <= 0:
                    messagebox.showerror("خطأ", "يجب أن يكون المبلغ أكبر من صفر")
                    return False
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                return False

            # التحقق من الوصف
            if not self.description_entry.get().strip():
                messagebox.showerror("خطأ", "يرجى إدخال وصف للمعاملة")
                return False

            # التحقق من التاريخ
            date_text = self.date_entry.get().strip()
            if not date_text:
                messagebox.showerror("خطأ", "يرجى إدخال التاريخ")
                return False

            try:
                datetime.strptime(date_text, "%Y-%m-%d")
            except ValueError:
                messagebox.showerror("خطأ", "تنسيق التاريخ غير صحيح (YYYY-MM-DD)")
                return False

            return True

        except Exception as e:
            self.logger.error(f"خطأ في التحقق من النموذج: {e}")
            return False

    def get_form_data(self):
        """جمع البيانات من النموذج"""
        return {
            'amount': float(self.amount_entry.get().strip()),
            'description': self.description_entry.get().strip(),
            'category': self.category_var.get(),
            'payment_method': self.payment_var.get(),
            'date': self.date_entry.get().strip(),
            'notes': self.notes_entry.get("1.0", "end-1c").strip()
        }

    def clear_form(self):
        """مسح النموذج"""
        try:
            self.amount_entry.delete(0, "end")
            self.description_entry.delete(0, "end")
            self.notes_entry.delete("1.0", "end")
            self.date_entry.delete(0, "end")
            self.date_entry.insert(0, date.today().strftime("%Y-%m-%d"))

            # إعادة تعيين الفئة الافتراضية
            self.on_type_change()

            # إعادة تعيين طريقة الدفع
            self.payment_var.set("نقدي")

            # إلغاء التحديد
            self.current_transaction = None
            self.update_button.configure(state="disabled")
            self.delete_button.configure(state="disabled")

        except Exception as e:
            self.logger.error(f"خطأ في مسح النموذج: {e}")

    def load_transactions(self):
        """تحميل المعاملات من قاعدة البيانات"""
        try:
            # مسح الجدول الحالي
            for item in self.tree.get_children():
                self.tree.delete(item)

            # استعلام المعاملات
            query = """
                SELECT id, type, amount, description, payment_method, date, notes
                FROM transactions
                ORDER BY date DESC, id DESC
            """

            rows = self.db_manager.execute_query(query)
            self.transactions_data = []

            for row in rows:
                # تحويل البيانات للعرض
                transaction_id = row[0]
                trans_type = "📈 دخل" if row[1] == "income" else "📉 مصروف"
                amount = f"{row[2]:,.0f} د.ع"
                description = row[3]

                # تحويل طريقة الدفع للعربية
                payment_methods = {
                    "cash": "نقدي",
                    "card": "بطاقة",
                    "bank": "تحويل بنكي",
                    "digital": "محفظة رقمية"
                }
                payment_method = payment_methods.get(row[4], row[4])

                transaction_date = row[5]

                # إضافة للجدول
                item = self.tree.insert("", "end", values=(
                    trans_type, amount, description, "عامة", payment_method, transaction_date
                ))

                # حفظ البيانات الأصلية
                self.transactions_data.append({
                    'item_id': item,
                    'id': transaction_id,
                    'type': row[1],
                    'amount': row[2],
                    'description': description,
                    'payment_method': row[4],
                    'date': transaction_date,
                    'notes': row[6] if row[6] else ""
                })

            self.logger.info(f"تم تحميل {len(rows)} معاملة")

        except Exception as e:
            self.logger.error(f"خطأ في تحميل المعاملات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل المعاملات: {e}")

    def update_statistics(self):
        """تحديث الإحصائيات"""
        try:
            # حساب إجمالي الدخل
            income_query = "SELECT SUM(amount) FROM transactions WHERE type='income'"
            income_result = self.db_manager.execute_query(income_query)
            total_income = income_result[0][0] if income_result[0][0] else 0

            # حساب إجمالي المصروفات
            expense_query = "SELECT SUM(amount) FROM transactions WHERE type='expense'"
            expense_result = self.db_manager.execute_query(expense_query)
            total_expenses = expense_result[0][0] if expense_result[0][0] else 0

            # حساب الرصيد
            balance = total_income - total_expenses

            # حساب عدد المعاملات
            count_query = "SELECT COUNT(*) FROM transactions"
            count_result = self.db_manager.execute_query(count_query)
            total_count = count_result[0][0] if count_result[0][0] else 0

            # تحديث التسميات
            self.income_label.configure(text=f"📈 إجمالي الدخل: {total_income:,.0f} د.ع")
            self.expense_label.configure(text=f"📉 إجمالي المصروفات: {total_expenses:,.0f} د.ع")

            # تلوين الرصيد حسب القيمة
            balance_color = "green" if balance >= 0 else "red"
            self.balance_label.configure(
                text=f"💰 الرصيد: {balance:,.0f} د.ع",
                text_color=balance_color
            )

            self.count_label.configure(text=f"📊 عدد المعاملات: {total_count}")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث الإحصائيات: {e}")

    def on_transaction_select(self, event):
        """عند اختيار معاملة من الجدول"""
        try:
            selection = self.tree.selection()
            if not selection:
                return

            # العثور على البيانات الأصلية
            selected_item = selection[0]
            transaction_data = None

            for data in self.transactions_data:
                if data['item_id'] == selected_item:
                    transaction_data = data
                    break

            if transaction_data:
                # تعبئة النموذج بالبيانات
                self.fill_form_with_data(transaction_data)

                # تفعيل أزرار التحديث والحذف
                self.current_transaction = transaction_data['id']
                self.update_button.configure(state="normal")
                self.delete_button.configure(state="normal")

        except Exception as e:
            self.logger.error(f"خطأ في اختيار المعاملة: {e}")

    def on_transaction_double_click(self, event):
        """عند النقر المزدوج على معاملة"""
        try:
            # نفس وظيفة الاختيار
            self.on_transaction_select(event)

        except Exception as e:
            self.logger.error(f"خطأ في النقر المزدوج: {e}")

    def fill_form_with_data(self, transaction_data):
        """تعبئة النموذج ببيانات المعاملة"""
        try:
            # مسح النموذج أولاً
            self.clear_form()

            # تعيين نوع المعاملة
            self.transaction_type_var.set(transaction_data['type'])
            self.on_type_change()

            # تعبئة البيانات
            self.amount_entry.insert(0, str(transaction_data['amount']))
            self.description_entry.insert(0, transaction_data['description'])
            self.date_entry.delete(0, "end")
            self.date_entry.insert(0, transaction_data['date'])

            # تعيين طريقة الدفع
            payment_methods = {
                "cash": "نقدي",
                "card": "بطاقة",
                "bank": "تحويل بنكي",
                "digital": "محفظة رقمية"
            }
            payment_arabic = payment_methods.get(transaction_data['payment_method'], "نقدي")
            self.payment_var.set(payment_arabic)

            # تعبئة الملاحظات
            if transaction_data['notes']:
                self.notes_entry.insert("1.0", transaction_data['notes'])

        except Exception as e:
            self.logger.error(f"خطأ في تعبئة النموذج: {e}")

    def on_search(self, event):
        """البحث في المعاملات"""
        try:
            search_term = self.search_entry.get().lower()

            # مسح الجدول
            for item in self.tree.get_children():
                self.tree.delete(item)

            # فلترة البيانات
            filtered_data = []
            for data in self.transactions_data:
                if (search_term in data['description'].lower() or
                    search_term in str(data['amount']) or
                    search_term in data['date']):
                    filtered_data.append(data)

            # إعادة عرض البيانات المفلترة
            self.display_filtered_transactions(filtered_data)

        except Exception as e:
            self.logger.error(f"خطأ في البحث: {e}")

    def on_filter_change(self, value):
        """تغيير فلتر النوع"""
        try:
            filter_type = self.filter_var.get()

            # مسح الجدول
            for item in self.tree.get_children():
                self.tree.delete(item)

            # فلترة البيانات
            filtered_data = []
            for data in self.transactions_data:
                if filter_type == "الكل":
                    filtered_data.append(data)
                elif filter_type == "دخل" and data['type'] == "income":
                    filtered_data.append(data)
                elif filter_type == "مصروف" and data['type'] == "expense":
                    filtered_data.append(data)

            # إعادة عرض البيانات المفلترة
            self.display_filtered_transactions(filtered_data)

        except Exception as e:
            self.logger.error(f"خطأ في الفلترة: {e}")

    def display_filtered_transactions(self, filtered_data):
        """عرض المعاملات المفلترة"""
        try:
            for data in filtered_data:
                trans_type = "📈 دخل" if data['type'] == "income" else "📉 مصروف"
                amount = f"{data['amount']:,.0f} د.ع"

                payment_methods = {
                    "cash": "نقدي",
                    "card": "بطاقة",
                    "bank": "تحويل بنكي",
                    "digital": "محفظة رقمية"
                }
                payment_method = payment_methods.get(data['payment_method'], data['payment_method'])

                item = self.tree.insert("", "end", values=(
                    trans_type, amount, data['description'], "عامة", payment_method, data['date']
                ))

                # تحديث معرف العنصر
                data['item_id'] = item

        except Exception as e:
            self.logger.error(f"خطأ في عرض المعاملات المفلترة: {e}")

    def refresh_data(self):
        """تحديث البيانات"""
        try:
            self.load_transactions()
            self.update_statistics()
            self.clear_form()

            # مسح البحث والفلتر
            self.search_entry.delete(0, "end")
            self.filter_var.set("الكل")

            messagebox.showinfo("تم", "تم تحديث البيانات بنجاح!")

        except Exception as e:
            self.logger.error(f"خطأ في تحديث البيانات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ في تحديث البيانات: {e}")
