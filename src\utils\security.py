"""
مدير الأمان والتشفير
"""
import hashlib
import secrets
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import bcrypt
import logging
from typing import Optional, Tuple

class SecurityManager:
    """مدير الأمان والتشفير"""
    
    def __init__(self):
        """تهيئة مدير الأمان"""
        self.logger = logging.getLogger(__name__)
        self._encryption_key = None
    
    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور باستخدام bcrypt"""
        try:
            # تحويل كلمة المرور إلى bytes
            password_bytes = password.encode('utf-8')
            
            # إنشاء salt وتشفير كلمة المرور
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password_bytes, salt)
            
            # إرجاع النتيجة كـ string
            return hashed.decode('utf-8')
        except Exception as e:
            self.logger.error(f"خطأ في تشفير كلمة المرور: {e}")
            raise
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """التحقق من كلمة المرور"""
        try:
            password_bytes = password.encode('utf-8')
            hashed_bytes = hashed_password.encode('utf-8')
            
            return bcrypt.checkpw(password_bytes, hashed_bytes)
        except Exception as e:
            self.logger.error(f"خطأ في التحقق من كلمة المرور: {e}")
            return False
    
    def generate_key_from_password(self, password: str, salt: bytes = None) -> Tuple[bytes, bytes]:
        """إنشاء مفتاح تشفير من كلمة المرور"""
        try:
            if salt is None:
                salt = secrets.token_bytes(16)
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            return key, salt
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء مفتاح التشفير: {e}")
            raise
    
    def set_encryption_key(self, password: str, salt: bytes = None) -> bytes:
        """تعيين مفتاح التشفير"""
        try:
            key, salt = self.generate_key_from_password(password, salt)
            self._encryption_key = key
            return salt
        except Exception as e:
            self.logger.error(f"خطأ في تعيين مفتاح التشفير: {e}")
            raise
    
    def encrypt_data(self, data: str) -> str:
        """تشفير البيانات"""
        try:
            if self._encryption_key is None:
                raise ValueError("لم يتم تعيين مفتاح التشفير")
            
            fernet = Fernet(self._encryption_key)
            encrypted_data = fernet.encrypt(data.encode())
            
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            self.logger.error(f"خطأ في تشفير البيانات: {e}")
            raise
    
    def decrypt_data(self, encrypted_data: str) -> str:
        """فك تشفير البيانات"""
        try:
            if self._encryption_key is None:
                raise ValueError("لم يتم تعيين مفتاح التشفير")
            
            fernet = Fernet(self._encryption_key)
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode())
            decrypted_data = fernet.decrypt(encrypted_bytes)
            
            return decrypted_data.decode()
        except Exception as e:
            self.logger.error(f"خطأ في فك تشفير البيانات: {e}")
            raise
    
    def generate_secure_token(self, length: int = 32) -> str:
        """إنشاء رمز آمن عشوائي"""
        try:
            token = secrets.token_urlsafe(length)
            return token
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الرمز الآمن: {e}")
            raise
    
    def hash_data(self, data: str, algorithm: str = 'sha256') -> str:
        """تشفير البيانات باستخدام hash"""
        try:
            data_bytes = data.encode('utf-8')
            
            if algorithm == 'sha256':
                hash_obj = hashlib.sha256(data_bytes)
            elif algorithm == 'sha512':
                hash_obj = hashlib.sha512(data_bytes)
            elif algorithm == 'md5':
                hash_obj = hashlib.md5(data_bytes)
            else:
                raise ValueError(f"خوارزمية غير مدعومة: {algorithm}")
            
            return hash_obj.hexdigest()
        except Exception as e:
            self.logger.error(f"خطأ في تشفير البيانات: {e}")
            raise
    
    def validate_password_strength(self, password: str) -> Tuple[bool, list]:
        """التحقق من قوة كلمة المرور"""
        errors = []
        
        # الحد الأدنى للطول
        if len(password) < 8:
            errors.append("كلمة المرور يجب أن تكون 8 أحرف على الأقل")
        
        # وجود أحرف كبيرة
        if not any(c.isupper() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل")
        
        # وجود أحرف صغيرة
        if not any(c.islower() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل")
        
        # وجود أرقام
        if not any(c.isdigit() for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على رقم واحد على الأقل")
        
        # وجود رموز خاصة
        special_chars = "!@#$%^&*()_+-=[]{}|;:,.<>?"
        if not any(c in special_chars for c in password):
            errors.append("كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل")
        
        is_strong = len(errors) == 0
        return is_strong, errors
    
    def sanitize_input(self, input_data: str) -> str:
        """تنظيف المدخلات من الرموز الضارة"""
        try:
            # إزالة الرموز الضارة الشائعة
            dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '|', '`']
            
            sanitized = input_data
            for char in dangerous_chars:
                sanitized = sanitized.replace(char, '')
            
            # إزالة المسافات الزائدة
            sanitized = sanitized.strip()
            
            return sanitized
        except Exception as e:
            self.logger.error(f"خطأ في تنظيف المدخلات: {e}")
            return ""
    
    def generate_session_token(self) -> str:
        """إنشاء رمز جلسة"""
        try:
            return self.generate_secure_token(64)
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء رمز الجلسة: {e}")
            raise
    
    def clear_encryption_key(self):
        """مسح مفتاح التشفير من الذاكرة"""
        self._encryption_key = None
        self.logger.info("تم مسح مفتاح التشفير من الذاكرة")

# مثيل عام لمدير الأمان
security_manager = SecurityManager()
