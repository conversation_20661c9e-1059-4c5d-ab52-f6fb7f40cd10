"""
إعداد تثبيت برنامج مصروف
"""
from setuptools import setup, find_packages
import os

# قراءة محتوى README
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# قراءة المتطلبات
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="masroof",
    version="1.0.0",
    author="Masroof Team",
    author_email="<EMAIL>",
    description="برنامج إدارة المصروفات الشخصية مع الذكاء الصناعي",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/masroof-team/masroof",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Office/Business :: Financial",
        "Topic :: Office/Business :: Financial :: Accounting",
        "Natural Language :: Arabic",
    ],
    python_requires=">=3.11",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "masroof=src.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "src": ["config/*", "ui/assets/*"],
    },
    keywords="finance, accounting, expenses, budget, arabic, personal finance",
    project_urls={
        "Bug Reports": "https://github.com/masroof-team/masroof/issues",
        "Source": "https://github.com/masroof-team/masroof",
        "Documentation": "https://masroof.app/docs",
    },
)
