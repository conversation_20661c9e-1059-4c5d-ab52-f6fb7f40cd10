# سجل التغييرات - برنامج مصروف

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور]

### مخطط للإضافة
- دعم العملات المتعددة
- تزامن البيانات مع السحابة
- تطبيق الجوال المصاحب
- تكامل مع البنوك المحلية
- تقارير ضريبية
- نظام الفئات المخصصة
- واجهة برمجة التطبيقات (API)

## [1.0.0] - 2025-05-28

### أضيف
- 🎉 **الإصدار الأول من برنامج مصروف**
- 💰 **النظام الأساسي**
  - واجهة مستخدم حديثة باستخدام CustomTkinter
  - دعم كامل للغة العربية
  - قاعدة بيانات SQLite محلية
  - نظام أمان متقدم مع التشفير

- 📊 **لوحة التحكم**
  - عرض الرصيد الإجمالي
  - ملخص الدخل والمصروفات
  - بطاقات معلومات تفاعلية
  - رسوم بيانية أساسية

- 💳 **إدارة المعاملات**
  - إضافة وتعديل المعاملات
  - تصنيف حسب الفئات
  - دعم طرق الدفع المختلفة
  - إرفاق الملاحظات والعلامات

- 💸 **إدارة الديون**
  - تتبع جميع أنواع الديون
  - حساب الفوائد والأقساط
  - تسجيل دفعات السداد
  - تتبع حالة الديون

- 📋 **نظام الميزانيات**
  - إنشاء ميزانيات للفئات
  - مراقبة الإنفاق
  - تنبيهات تجاوز الحدود
  - فترات زمنية مختلفة

- 🎯 **الأهداف المالية**
  - تحديد أهداف الادخار
  - تتبع التقدم
  - تنبيهات الإنجاز

- 🤖 **الذكاء الصناعي**
  - تحليل أنماط الإنفاق
  - التنبؤ بالمصروفات المستقبلية
  - نصائح مالية ذكية
  - تحليل الاتجاهات

- 📈 **التقارير والتحليلات**
  - تقارير مفصلة
  - رسوم بيانية متنوعة
  - تحليل الأداء المالي
  - مقارنات زمنية

- 🔔 **نظام الإشعارات**
  - تذكيرات مواعيد الديون
  - تنبيهات تجاوز الميزانية
  - ملخص يومي
  - إشعارات الإنجازات

- 📤 **التصدير والاستيراد**
  - تصدير إلى Excel و CSV
  - إنشاء تقارير PDF
  - رسوم بيانية قابلة للحفظ
  - نسخ احتياطية

- 🔒 **الأمان والخصوصية**
  - تشفير البيانات الحساسة
  - حماية بكلمة مرور
  - نسخ احتياطية آمنة
  - تخزين محلي فقط

- ⚙️ **الإعدادات**
  - تخصيص الواجهة
  - إعدادات العملة
  - تفضيلات الإشعارات
  - خيارات النسخ الاحتياطي

### التقنيات المستخدمة
- **Python 3.11+** - اللغة الأساسية
- **CustomTkinter** - واجهة المستخدم الحديثة
- **SQLite** - قاعدة البيانات المحلية
- **pandas & numpy** - معالجة البيانات
- **scikit-learn** - التعلم الآلي
- **matplotlib & plotly** - الرسوم البيانية
- **cryptography** - التشفير والأمان
- **arabic-reshaper & python-bidi** - دعم اللغة العربية

### متطلبات النظام
- **نظام التشغيل**: Windows 10+, macOS 10.14+, Linux Ubuntu 18.04+
- **Python**: 3.11 أو أحدث
- **الذاكرة**: 4 GB RAM (مستحسن 8 GB)
- **التخزين**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 أو أعلى

### الملفات المضافة
```
masroof/
├── src/
│   ├── main.py                 # نقطة البداية
│   ├── config/
│   │   ├── __init__.py
│   │   └── settings.py         # الإعدادات الأساسية
│   ├── database/
│   │   ├── __init__.py
│   │   ├── models.py           # نماذج البيانات
│   │   └── database.py         # مدير قاعدة البيانات
│   ├── ui/
│   │   ├── __init__.py
│   │   └── main_window.py      # النافذة الرئيسية
│   ├── ai/
│   │   ├── __init__.py
│   │   ├── analyzer.py         # محلل البيانات
│   │   ├── predictor.py        # نظام التنبؤات
│   │   └── advisor.py          # المستشار الذكي
│   └── utils/
│       ├── __init__.py
│       ├── notifications.py    # مدير الإشعارات
│       ├── export.py           # مدير التصدير
│       └── security.py         # مدير الأمان
├── requirements.txt            # المتطلبات
├── setup.py                   # إعداد التثبيت
├── run.py                     # ملف التشغيل المبسط
├── README.md                  # دليل المشروع
├── USER_GUIDE.md              # دليل المستخدم
├── CHANGELOG.md               # سجل التغييرات
├── LICENSE                    # الترخيص
└── .gitignore                # ملفات Git المتجاهلة
```

### إحصائيات الإصدار
- **عدد الملفات**: 20+ ملف
- **أسطر الكود**: 3000+ سطر
- **الوظائف**: 100+ وظيفة
- **الفئات**: 15+ فئة
- **وقت التطوير**: شهرين

### شكر خاص
- فريق تطوير CustomTkinter لواجهة المستخدم الرائعة
- مجتمع Python للمكتبات المفيدة
- المساهمين في مكتبات دعم اللغة العربية
- المختبرين الأوائل لملاحظاتهم القيمة

---

## تنسيق سجل التغييرات

### أنواع التغييرات
- `أضيف` للميزات الجديدة
- `غُيّر` للتغييرات في الوظائف الموجودة
- `مُهمل` للميزات التي ستُزال قريباً
- `أُزيل` للميزات المُزالة
- `أُصلح` لإصلاح الأخطاء
- `أمان` لإصلاحات الأمان

### تنسيق الإصدارات
- **الإصدار الرئيسي** (X.0.0): تغييرات كبيرة وغير متوافقة
- **الإصدار الثانوي** (0.X.0): ميزات جديدة متوافقة
- **إصدار الإصلاح** (0.0.X): إصلاحات الأخطاء

---

**للمزيد من المعلومات، راجع [دليل المستخدم](USER_GUIDE.md) أو زر [موقعنا الإلكتروني](https://masroof.app)**
