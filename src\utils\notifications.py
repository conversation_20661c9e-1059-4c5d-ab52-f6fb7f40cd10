"""
مدير الإشعارات
"""
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from plyer import notification
import schedule
import threading
import time

from ..config.settings import NOTIFICATION_CONFIG, MESSAGES, UI_CONFIG

class NotificationManager:
    """مدير الإشعارات"""
    
    def __init__(self):
        """تهيئة مدير الإشعارات"""
        self.logger = logging.getLogger(__name__)
        self.enabled = NOTIFICATION_CONFIG['enable_notifications']
        self.scheduler_thread = None
        self.running = False
        
        # بدء جدولة الإشعارات
        if self.enabled:
            self.start_scheduler()
    
    def show_notification(self, title: str, message: str, timeout: int = 10, 
                         notification_type: str = "info") -> bool:
        """عرض إشعار للمستخدم"""
        try:
            if not self.enabled:
                return False
            
            # تحديد أيقونة الإشعار حسب النوع
            icon_map = {
                'info': '💰',
                'warning': '⚠️',
                'error': '❌',
                'success': '✅',
                'debt': '💸',
                'budget': '📊'
            }
            
            icon = icon_map.get(notification_type, '💰')
            full_title = f"{icon} {title}"
            
            notification.notify(
                title=full_title,
                message=message,
                timeout=timeout,
                app_name="مصروف",
                app_icon=None  # يمكن إضافة مسار أيقونة هنا
            )
            
            self.logger.info(f"تم عرض إشعار: {title} - {message}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في عرض الإشعار: {e}")
            return False
    
    def notify_debt_reminder(self, debt_name: str, due_date: datetime, 
                           amount: float, days_remaining: int):
        """إشعار تذكير بموعد استحقاق الدين"""
        try:
            if days_remaining == 1:
                title = "تذكير عاجل - استحقاق دين غداً"
                message = f"الدين '{debt_name}' مستحق غداً\nالمبلغ: {amount:,.0f} د.ع"
                notification_type = "warning"
            elif days_remaining <= 3:
                title = f"تذكير - استحقاق دين خلال {days_remaining} أيام"
                message = f"الدين '{debt_name}' مستحق في {due_date.strftime('%Y-%m-%d')}\nالمبلغ: {amount:,.0f} د.ع"
                notification_type = "warning"
            else:
                title = f"تذكير - استحقاق دين خلال {days_remaining} أيام"
                message = f"الدين '{debt_name}' مستحق في {due_date.strftime('%Y-%m-%d')}\nالمبلغ: {amount:,.0f} د.ع"
                notification_type = "debt"
            
            self.show_notification(title, message, timeout=15, 
                                 notification_type=notification_type)
            
        except Exception as e:
            self.logger.error(f"خطأ في إشعار تذكير الدين: {e}")
    
    def notify_budget_warning(self, category_name: str, spent_amount: float, 
                            budget_amount: float, percentage: float):
        """إشعار تحذير تجاوز الميزانية"""
        try:
            if percentage >= 100:
                title = "تحذير - تجاوز الميزانية"
                message = f"تم تجاوز ميزانية '{category_name}'\nالمصروف: {spent_amount:,.0f} د.ع\nالميزانية: {budget_amount:,.0f} د.ع"
                notification_type = "error"
            elif percentage >= NOTIFICATION_CONFIG['budget_warning_percentage']:
                title = "تحذير - اقتراب من حد الميزانية"
                message = f"وصلت إلى {percentage:.0f}% من ميزانية '{category_name}'\nالمصروف: {spent_amount:,.0f} د.ع\nالميزانية: {budget_amount:,.0f} د.ع"
                notification_type = "warning"
            else:
                return
            
            self.show_notification(title, message, timeout=15, 
                                 notification_type=notification_type)
            
        except Exception as e:
            self.logger.error(f"خطأ في إشعار تحذير الميزانية: {e}")
    
    def notify_daily_summary(self, income: float, expenses: float, balance: float):
        """إشعار الملخص اليومي"""
        try:
            title = "الملخص اليومي"
            message = f"الدخل اليوم: {income:,.0f} د.ع\nالمصروفات: {expenses:,.0f} د.ع\nالرصيد: {balance:,.0f} د.ع"
            
            self.show_notification(title, message, timeout=20, 
                                 notification_type="info")
            
        except Exception as e:
            self.logger.error(f"خطأ في إشعار الملخص اليومي: {e}")
    
    def notify_transaction_added(self, transaction_type: str, amount: float, 
                               category: str):
        """إشعار إضافة معاملة جديدة"""
        try:
            if transaction_type == "income":
                title = "تم إضافة دخل جديد"
                icon = "📈"
            else:
                title = "تم إضافة مصروف جديد"
                icon = "📉"
            
            message = f"{icon} {amount:,.0f} د.ع\nالفئة: {category}"
            
            self.show_notification(title, message, timeout=5, 
                                 notification_type="success")
            
        except Exception as e:
            self.logger.error(f"خطأ في إشعار إضافة المعاملة: {e}")
    
    def notify_goal_achieved(self, goal_name: str, target_amount: float):
        """إشعار تحقيق هدف مالي"""
        try:
            title = "🎉 تهانينا! تم تحقيق الهدف"
            message = f"تم تحقيق الهدف '{goal_name}'\nالمبلغ المستهدف: {target_amount:,.0f} د.ع"
            
            self.show_notification(title, message, timeout=20, 
                                 notification_type="success")
            
        except Exception as e:
            self.logger.error(f"خطأ في إشعار تحقيق الهدف: {e}")
    
    def schedule_debt_reminders(self, debts: List[Dict[str, Any]]):
        """جدولة تذكيرات الديون"""
        try:
            for debt in debts:
                due_date = debt.get('due_date')
                if not due_date:
                    continue
                
                # تحويل التاريخ إذا كان string
                if isinstance(due_date, str):
                    due_date = datetime.strptime(due_date, '%Y-%m-%d').date()
                
                today = datetime.now().date()
                days_remaining = (due_date - today).days
                
                # جدولة التذكيرات حسب الأيام المحددة في الإعدادات
                for reminder_days in NOTIFICATION_CONFIG['debt_reminder_days']:
                    if days_remaining == reminder_days:
                        self.notify_debt_reminder(
                            debt['name'],
                            datetime.combine(due_date, datetime.min.time()),
                            debt['remaining_amount'],
                            days_remaining
                        )
                        break
            
        except Exception as e:
            self.logger.error(f"خطأ في جدولة تذكيرات الديون: {e}")
    
    def start_scheduler(self):
        """بدء جدولة الإشعارات"""
        try:
            if self.running:
                return
            
            # جدولة الملخص اليومي
            daily_time = NOTIFICATION_CONFIG['daily_summary_time']
            schedule.every().day.at(daily_time).do(self._daily_summary_job)
            
            # جدولة فحص الديون يومياً
            schedule.every().day.at("09:00").do(self._check_debts_job)
            
            # جدولة فحص الميزانيات كل ساعة
            schedule.every().hour.do(self._check_budgets_job)
            
            self.running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            self.logger.info("تم بدء جدولة الإشعارات")
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء جدولة الإشعارات: {e}")
    
    def stop_scheduler(self):
        """إيقاف جدولة الإشعارات"""
        try:
            self.running = False
            schedule.clear()
            self.logger.info("تم إيقاف جدولة الإشعارات")
            
        except Exception as e:
            self.logger.error(f"خطأ في إيقاف جدولة الإشعارات: {e}")
    
    def _run_scheduler(self):
        """تشغيل جدولة الإشعارات في خيط منفصل"""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # فحص كل دقيقة
            except Exception as e:
                self.logger.error(f"خطأ في تشغيل جدولة الإشعارات: {e}")
                time.sleep(60)
    
    def _daily_summary_job(self):
        """مهمة الملخص اليومي"""
        try:
            # هنا يمكن إضافة منطق جلب البيانات من قاعدة البيانات
            # وحساب الملخص اليومي
            self.logger.info("تم تشغيل مهمة الملخص اليومي")
            
        except Exception as e:
            self.logger.error(f"خطأ في مهمة الملخص اليومي: {e}")
    
    def _check_debts_job(self):
        """مهمة فحص الديون"""
        try:
            # هنا يمكن إضافة منطق جلب الديون من قاعدة البيانات
            # وفحص مواعيد الاستحقاق
            self.logger.info("تم تشغيل مهمة فحص الديون")
            
        except Exception as e:
            self.logger.error(f"خطأ في مهمة فحص الديون: {e}")
    
    def _check_budgets_job(self):
        """مهمة فحص الميزانيات"""
        try:
            # هنا يمكن إضافة منطق جلب الميزانيات من قاعدة البيانات
            # وفحص تجاوز الحدود
            self.logger.info("تم تشغيل مهمة فحص الميزانيات")
            
        except Exception as e:
            self.logger.error(f"خطأ في مهمة فحص الميزانيات: {e}")
    
    def enable_notifications(self):
        """تفعيل الإشعارات"""
        self.enabled = True
        if not self.running:
            self.start_scheduler()
        self.logger.info("تم تفعيل الإشعارات")
    
    def disable_notifications(self):
        """تعطيل الإشعارات"""
        self.enabled = False
        self.stop_scheduler()
        self.logger.info("تم تعطيل الإشعارات")

# مثيل عام لمدير الإشعارات
notification_manager = NotificationManager()
