#!/usr/bin/env python3
"""
إضافة بيانات تجريبية لاختبار البرنامج
"""

import sys
from pathlib import Path
from datetime import datetime, date, timedelta
import random

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.database.database import DatabaseManager

def add_sample_transactions():
    """إضافة معاملات تجريبية"""
    
    db_manager = DatabaseManager()
    
    # بيانات تجريبية للدخل
    income_data = [
        {
            'type': 'income',
            'amount': 2500000,  # 2.5 مليون دينار
            'description': 'راتب أساسي - شهر ديسمبر',
            'category_id': 1,
            'payment_method': 'bank',
            'date': '2025-05-01',
            'notes': 'راتب الوظيفة الأساسية'
        },
        {
            'type': 'income', 
            'amount': 800000,   # 800 ألف دينار
            'description': 'راتب إضافي - عمل جانبي',
            'category_id': 1,
            'payment_method': 'cash',
            'date': '2025-05-05',
            'notes': 'دخل من العمل الجانبي'
        },
        {
            'type': 'income',
            'amount': 500000,   # 500 ألف دينار
            'description': 'مكافأة أداء',
            'category_id': 1,
            'payment_method': 'bank',
            'date': '2025-05-10',
            'notes': 'مكافأة نهاية السنة'
        },
        {
            'type': 'income',
            'amount': 300000,   # 300 ألف دينار
            'description': 'استثمار - أرباح أسهم',
            'category_id': 1,
            'payment_method': 'bank',
            'date': '2025-05-15',
            'notes': 'أرباح من الاستثمارات'
        }
    ]
    
    # بيانات تجريبية للمصروفات
    expense_data = [
        {
            'type': 'expense',
            'amount': 800000,   # 800 ألف دينار
            'description': 'إيجار المنزل',
            'category_id': 1,
            'payment_method': 'bank',
            'date': '2025-05-01',
            'notes': 'إيجار شهر مايو'
        },
        {
            'type': 'expense',
            'amount': 200000,   # 200 ألف دينار
            'description': 'فواتير الكهرباء والماء',
            'category_id': 1,
            'payment_method': 'cash',
            'date': '2025-05-03',
            'notes': 'فواتير الخدمات'
        },
        {
            'type': 'expense',
            'amount': 150000,   # 150 ألف دينار
            'description': 'تسوق البقالة',
            'category_id': 1,
            'payment_method': 'cash',
            'date': '2025-05-05',
            'notes': 'مشتريات أسبوعية'
        },
        {
            'type': 'expense',
            'amount': 100000,   # 100 ألف دينار
            'description': 'وقود السيارة',
            'category_id': 1,
            'payment_method': 'cash',
            'date': '2025-05-07',
            'notes': 'تعبئة خزان الوقود'
        },
        {
            'type': 'expense',
            'amount': 75000,    # 75 ألف دينار
            'description': 'وجبة عشاء في مطعم',
            'category_id': 1,
            'payment_method': 'card',
            'date': '2025-05-10',
            'notes': 'عشاء مع الأصدقاء'
        },
        {
            'type': 'expense',
            'amount': 50000,    # 50 ألف دينار
            'description': 'أدوية',
            'category_id': 1,
            'payment_method': 'cash',
            'date': '2025-05-12',
            'notes': 'أدوية ضرورية'
        },
        {
            'type': 'expense',
            'amount': 120000,   # 120 ألف دينار
            'description': 'ملابس جديدة',
            'category_id': 1,
            'payment_method': 'card',
            'date': '2025-05-15',
            'notes': 'ملابس للعمل'
        },
        {
            'type': 'expense',
            'amount': 80000,    # 80 ألف دينار
            'description': 'صيانة السيارة',
            'category_id': 1,
            'payment_method': 'cash',
            'date': '2025-05-18',
            'notes': 'تغيير زيت وفلاتر'
        }
    ]
    
    # دمج جميع البيانات
    all_transactions = income_data + expense_data
    
    # إدراج البيانات
    query = """
        INSERT INTO transactions (type, amount, description, category_id, 
                                payment_method, date, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    """
    
    success_count = 0
    
    for transaction in all_transactions:
        try:
            params = (
                transaction['type'],
                transaction['amount'],
                transaction['description'],
                transaction['category_id'],
                transaction['payment_method'],
                transaction['date'],
                transaction['notes']
            )
            
            transaction_id = db_manager.execute_insert(query, params)
            if transaction_id:
                success_count += 1
                print(f"✅ تم إضافة: {transaction['description']} - {transaction['amount']:,} د.ع")
            
        except Exception as e:
            print(f"❌ خطأ في إضافة {transaction['description']}: {e}")
    
    print(f"\n🎉 تم إضافة {success_count} معاملة من أصل {len(all_transactions)}")
    
    # عرض الإحصائيات
    show_statistics(db_manager)

def show_statistics(db_manager):
    """عرض إحصائيات سريعة"""
    try:
        # إجمالي الدخل
        income_query = "SELECT SUM(amount) FROM transactions WHERE type='income'"
        income_result = db_manager.execute_query(income_query)
        total_income = income_result[0][0] if income_result[0][0] else 0
        
        # إجمالي المصروفات
        expense_query = "SELECT SUM(amount) FROM transactions WHERE type='expense'"
        expense_result = db_manager.execute_query(expense_query)
        total_expenses = expense_result[0][0] if expense_result[0][0] else 0
        
        # الرصيد
        balance = total_income - total_expenses
        
        # عدد المعاملات
        count_query = "SELECT COUNT(*) FROM transactions"
        count_result = db_manager.execute_query(count_query)
        total_count = count_result[0][0] if count_result[0][0] else 0
        
        print("\n" + "="*50)
        print("📊 إحصائيات سريعة:")
        print("="*50)
        print(f"📈 إجمالي الدخل: {total_income:,} د.ع")
        print(f"📉 إجمالي المصروفات: {total_expenses:,} د.ع")
        print(f"💰 الرصيد: {balance:,} د.ع")
        print(f"📊 عدد المعاملات: {total_count}")
        print("="*50)
        
        if balance > 0:
            print("✅ وضعك المالي جيد! لديك فائض في الرصيد")
        elif balance == 0:
            print("⚖️ رصيدك متوازن")
        else:
            print("⚠️ تحذير: لديك عجز في الرصيد")
            
    except Exception as e:
        print(f"❌ خطأ في عرض الإحصائيات: {e}")

def clear_all_data():
    """مسح جميع البيانات (للاختبار)"""
    db_manager = DatabaseManager()
    
    try:
        # تأكيد المسح
        response = input("\n⚠️ هل أنت متأكد من مسح جميع المعاملات؟ (نعم/لا): ")
        
        if response.lower() in ['نعم', 'yes', 'y']:
            query = "DELETE FROM transactions"
            rows_affected = db_manager.execute_update(query)
            print(f"🗑️ تم مسح {rows_affected} معاملة")
        else:
            print("❌ تم إلغاء العملية")
            
    except Exception as e:
        print(f"❌ خطأ في مسح البيانات: {e}")

def main():
    """الوظيفة الرئيسية"""
    print("🚀 أداة إضافة البيانات التجريبية")
    print("="*40)
    print("1. إضافة بيانات تجريبية")
    print("2. مسح جميع البيانات")
    print("3. عرض الإحصائيات فقط")
    print("4. خروج")
    print("="*40)
    
    choice = input("اختر رقم العملية: ")
    
    if choice == "1":
        print("\n📝 إضافة البيانات التجريبية...")
        add_sample_transactions()
    elif choice == "2":
        clear_all_data()
    elif choice == "3":
        db_manager = DatabaseManager()
        show_statistics(db_manager)
    elif choice == "4":
        print("👋 وداعاً!")
    else:
        print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    main()
