"""
نظام التنبؤات المالية بالذكاء الصناعي
"""
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from typing import List, Dict, Any, Tuple, Optional
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

from ..config.settings import AI_CONFIG

class FinancialPredictor:
    """نظام التنبؤات المالية"""
    
    def __init__(self):
        """تهيئة نظام التنبؤات"""
        self.logger = logging.getLogger(__name__)
        self.min_data_points = AI_CONFIG['min_data_points']
        self.prediction_days = AI_CONFIG['prediction_days']
        self.confidence_threshold = AI_CONFIG['confidence_threshold']
        
        # نماذج التعلم الآلي
        self.expense_model = None
        self.income_model = None
        self.scaler = StandardScaler()
    
    def predict_future_expenses(self, transactions: List[Dict[str, Any]], 
                               days_ahead: int = None) -> Dict[str, Any]:
        """التنبؤ بالمصروفات المستقبلية"""
        try:
            if days_ahead is None:
                days_ahead = self.prediction_days
            
            if len(transactions) < self.min_data_points:
                return {
                    'status': 'insufficient_data',
                    'message': f'يحتاج التنبؤ إلى {self.min_data_points} معاملة على الأقل',
                    'data_points': len(transactions)
                }
            
            df = pd.DataFrame(transactions)
            df['date'] = pd.to_datetime(df['date'])
            
            # فلترة المصروفات فقط
            expenses = df[df['type'] == 'expense'].copy()
            
            if len(expenses) == 0:
                return {
                    'status': 'no_expenses',
                    'message': 'لا توجد مصروفات للتنبؤ'
                }
            
            # إعداد البيانات للتنبؤ
            daily_expenses = self._prepare_daily_data(expenses)
            
            # بناء النموذج والتنبؤ
            predictions = self._build_and_predict(daily_expenses, days_ahead, 'expense')
            
            # حساب الثقة في التنبؤ
            confidence = self._calculate_prediction_confidence(daily_expenses, predictions)
            
            # تحليل الاتجاهات
            trends = self._analyze_expense_trends(daily_expenses, predictions)
            
            return {
                'status': 'success',
                'predictions': predictions,
                'confidence': confidence,
                'trends': trends,
                'model_accuracy': self._get_model_accuracy('expense'),
                'prediction_period': {
                    'start_date': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d'),
                    'end_date': (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d'),
                    'days': days_ahead
                }
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ بالمصروفات: {e}")
            return {
                'status': 'error',
                'message': f'حدث خطأ في التنبؤ: {str(e)}'
            }
    
    def predict_future_income(self, transactions: List[Dict[str, Any]], 
                            days_ahead: int = None) -> Dict[str, Any]:
        """التنبؤ بالدخل المستقبلي"""
        try:
            if days_ahead is None:
                days_ahead = self.prediction_days
            
            if len(transactions) < self.min_data_points:
                return {
                    'status': 'insufficient_data',
                    'message': f'يحتاج التنبؤ إلى {self.min_data_points} معاملة على الأقل'
                }
            
            df = pd.DataFrame(transactions)
            df['date'] = pd.to_datetime(df['date'])
            
            # فلترة الدخل فقط
            income = df[df['type'] == 'income'].copy()
            
            if len(income) == 0:
                return {
                    'status': 'no_income',
                    'message': 'لا توجد بيانات دخل للتنبؤ'
                }
            
            # إعداد البيانات للتنبؤ
            daily_income = self._prepare_daily_data(income)
            
            # بناء النموذج والتنبؤ
            predictions = self._build_and_predict(daily_income, days_ahead, 'income')
            
            # حساب الثقة في التنبؤ
            confidence = self._calculate_prediction_confidence(daily_income, predictions)
            
            # تحليل الاتجاهات
            trends = self._analyze_income_trends(daily_income, predictions)
            
            return {
                'status': 'success',
                'predictions': predictions,
                'confidence': confidence,
                'trends': trends,
                'model_accuracy': self._get_model_accuracy('income'),
                'prediction_period': {
                    'start_date': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d'),
                    'end_date': (datetime.now() + timedelta(days=days_ahead)).strftime('%Y-%m-%d'),
                    'days': days_ahead
                }
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ بالدخل: {e}")
            return {
                'status': 'error',
                'message': f'حدث خطأ في التنبؤ: {str(e)}'
            }
    
    def predict_cash_flow(self, transactions: List[Dict[str, Any]], 
                         days_ahead: int = None) -> Dict[str, Any]:
        """التنبؤ بالتدفق النقدي"""
        try:
            if days_ahead is None:
                days_ahead = self.prediction_days
            
            # التنبؤ بالمصروفات والدخل
            expense_prediction = self.predict_future_expenses(transactions, days_ahead)
            income_prediction = self.predict_future_income(transactions, days_ahead)
            
            if expense_prediction['status'] != 'success' or income_prediction['status'] != 'success':
                return {
                    'status': 'error',
                    'message': 'فشل في التنبؤ بالتدفق النقدي'
                }
            
            # حساب التدفق النقدي
            cash_flow_predictions = []
            cumulative_flow = 0
            
            for i in range(days_ahead):
                daily_income = income_prediction['predictions'][i]['amount'] if i < len(income_prediction['predictions']) else 0
                daily_expense = expense_prediction['predictions'][i]['amount'] if i < len(expense_prediction['predictions']) else 0
                
                daily_flow = daily_income - daily_expense
                cumulative_flow += daily_flow
                
                prediction_date = (datetime.now() + timedelta(days=i+1)).strftime('%Y-%m-%d')
                
                cash_flow_predictions.append({
                    'date': prediction_date,
                    'income': round(daily_income, 2),
                    'expense': round(daily_expense, 2),
                    'net_flow': round(daily_flow, 2),
                    'cumulative_flow': round(cumulative_flow, 2)
                })
            
            # تحليل التدفق النقدي
            analysis = self._analyze_cash_flow(cash_flow_predictions)
            
            return {
                'status': 'success',
                'predictions': cash_flow_predictions,
                'analysis': analysis,
                'summary': {
                    'total_predicted_income': sum(p['income'] for p in cash_flow_predictions),
                    'total_predicted_expenses': sum(p['expense'] for p in cash_flow_predictions),
                    'net_cash_flow': sum(p['net_flow'] for p in cash_flow_predictions),
                    'final_cumulative': cash_flow_predictions[-1]['cumulative_flow'] if cash_flow_predictions else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في التنبؤ بالتدفق النقدي: {e}")
            return {
                'status': 'error',
                'message': f'حدث خطأ في التنبؤ: {str(e)}'
            }
    
    def _prepare_daily_data(self, transactions: pd.DataFrame) -> pd.DataFrame:
        """إعداد البيانات اليومية للتنبؤ"""
        try:
            # تجميع المعاملات حسب التاريخ
            daily_data = transactions.groupby(transactions['date'].dt.date).agg({
                'amount': 'sum'
            }).reset_index()
            
            # إنشاء سلسلة زمنية كاملة
            date_range = pd.date_range(
                start=daily_data['date'].min(),
                end=daily_data['date'].max(),
                freq='D'
            )
            
            full_series = pd.DataFrame({'date': date_range.date})
            full_series = full_series.merge(daily_data, on='date', how='left')
            full_series['amount'] = full_series['amount'].fillna(0)
            
            # إضافة ميزات زمنية
            full_series['date'] = pd.to_datetime(full_series['date'])
            full_series['day_of_week'] = full_series['date'].dt.dayofweek
            full_series['day_of_month'] = full_series['date'].dt.day
            full_series['month'] = full_series['date'].dt.month
            full_series['days_since_start'] = (full_series['date'] - full_series['date'].min()).dt.days
            
            # إضافة متوسطات متحركة
            full_series['ma_7'] = full_series['amount'].rolling(window=7, min_periods=1).mean()
            full_series['ma_30'] = full_series['amount'].rolling(window=30, min_periods=1).mean()
            
            return full_series
            
        except Exception as e:
            self.logger.error(f"خطأ في إعداد البيانات اليومية: {e}")
            raise
    
    def _build_and_predict(self, data: pd.DataFrame, days_ahead: int, 
                          model_type: str) -> List[Dict[str, Any]]:
        """بناء النموذج والتنبؤ"""
        try:
            # إعداد الميزات والهدف
            features = ['day_of_week', 'day_of_month', 'month', 'days_since_start', 'ma_7', 'ma_30']
            X = data[features].fillna(0)
            y = data['amount']
            
            # تدريب النموذج
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
            
            # حفظ النموذج
            if model_type == 'expense':
                self.expense_model = model
            else:
                self.income_model = model
            
            # إنشاء بيانات للتنبؤ
            last_date = data['date'].max()
            predictions = []
            
            for i in range(1, days_ahead + 1):
                future_date = last_date + timedelta(days=i)
                
                # إعداد الميزات للتاريخ المستقبلي
                future_features = {
                    'day_of_week': future_date.dayofweek,
                    'day_of_month': future_date.day,
                    'month': future_date.month,
                    'days_since_start': (future_date - data['date'].min()).days,
                    'ma_7': data['amount'].tail(7).mean(),
                    'ma_30': data['amount'].tail(30).mean()
                }
                
                X_future = pd.DataFrame([future_features])
                predicted_amount = model.predict(X_future)[0]
                
                # التأكد من أن التنبؤ ليس سالباً
                predicted_amount = max(0, predicted_amount)
                
                predictions.append({
                    'date': future_date.strftime('%Y-%m-%d'),
                    'amount': round(predicted_amount, 2),
                    'day_of_week': future_date.strftime('%A')
                })
            
            return predictions
            
        except Exception as e:
            self.logger.error(f"خطأ في بناء النموذج والتنبؤ: {e}")
            raise
    
    def _calculate_prediction_confidence(self, historical_data: pd.DataFrame, 
                                       predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """حساب مستوى الثقة في التنبؤ"""
        try:
            # حساب التباين في البيانات التاريخية
            historical_variance = historical_data['amount'].var()
            historical_mean = historical_data['amount'].mean()
            
            # حساب معامل التباين
            cv = (historical_variance ** 0.5) / historical_mean if historical_mean > 0 else 1
            
            # تحديد مستوى الثقة بناءً على معامل التباين
            if cv < 0.2:
                confidence_level = 'high'
                confidence_score = 0.9
            elif cv < 0.5:
                confidence_level = 'medium'
                confidence_score = 0.7
            else:
                confidence_level = 'low'
                confidence_score = 0.5
            
            return {
                'level': confidence_level,
                'score': confidence_score,
                'coefficient_of_variation': round(cv, 3),
                'explanation': self._get_confidence_explanation(confidence_level)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في حساب الثقة: {e}")
            return {'level': 'unknown', 'score': 0.5}
    
    def _analyze_expense_trends(self, historical_data: pd.DataFrame, 
                              predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل اتجاهات المصروفات"""
        try:
            # حساب الاتجاه التاريخي
            recent_data = historical_data.tail(30)
            if len(recent_data) > 1:
                trend_slope = np.polyfit(range(len(recent_data)), recent_data['amount'], 1)[0]
            else:
                trend_slope = 0
            
            # تحليل التنبؤات
            predicted_amounts = [p['amount'] for p in predictions]
            predicted_trend = np.polyfit(range(len(predicted_amounts)), predicted_amounts, 1)[0] if len(predicted_amounts) > 1 else 0
            
            # تحديد الاتجاه
            if predicted_trend > 5:
                trend_direction = 'increasing'
                trend_description = 'متزايد'
            elif predicted_trend < -5:
                trend_direction = 'decreasing'
                trend_description = 'متناقص'
            else:
                trend_direction = 'stable'
                trend_description = 'مستقر'
            
            return {
                'direction': trend_direction,
                'description': trend_description,
                'slope': round(predicted_trend, 2),
                'historical_slope': round(trend_slope, 2),
                'average_predicted': round(np.mean(predicted_amounts), 2),
                'max_predicted': round(max(predicted_amounts), 2),
                'min_predicted': round(min(predicted_amounts), 2)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل اتجاهات المصروفات: {e}")
            return {}
    
    def _analyze_income_trends(self, historical_data: pd.DataFrame, 
                             predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل اتجاهات الدخل"""
        try:
            # مشابه لتحليل المصروفات
            return self._analyze_expense_trends(historical_data, predictions)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل اتجاهات الدخل: {e}")
            return {}
    
    def _analyze_cash_flow(self, cash_flow_predictions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """تحليل التدفق النقدي المتوقع"""
        try:
            positive_days = sum(1 for p in cash_flow_predictions if p['net_flow'] > 0)
            negative_days = sum(1 for p in cash_flow_predictions if p['net_flow'] < 0)
            
            max_positive_flow = max((p['net_flow'] for p in cash_flow_predictions), default=0)
            max_negative_flow = min((p['net_flow'] for p in cash_flow_predictions), default=0)
            
            # البحث عن أطول فترة سالبة
            longest_negative_streak = 0
            current_negative_streak = 0
            
            for prediction in cash_flow_predictions:
                if prediction['net_flow'] < 0:
                    current_negative_streak += 1
                    longest_negative_streak = max(longest_negative_streak, current_negative_streak)
                else:
                    current_negative_streak = 0
            
            return {
                'positive_days': positive_days,
                'negative_days': negative_days,
                'positive_percentage': round(positive_days / len(cash_flow_predictions) * 100, 1),
                'max_positive_flow': round(max_positive_flow, 2),
                'max_negative_flow': round(max_negative_flow, 2),
                'longest_negative_streak': longest_negative_streak,
                'risk_level': self._assess_cash_flow_risk(cash_flow_predictions)
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تحليل التدفق النقدي: {e}")
            return {}
    
    def _assess_cash_flow_risk(self, cash_flow_predictions: List[Dict[str, Any]]) -> str:
        """تقييم مخاطر التدفق النقدي"""
        try:
            negative_days = sum(1 for p in cash_flow_predictions if p['net_flow'] < 0)
            total_days = len(cash_flow_predictions)
            negative_percentage = negative_days / total_days * 100
            
            final_cumulative = cash_flow_predictions[-1]['cumulative_flow'] if cash_flow_predictions else 0
            
            if negative_percentage > 70 or final_cumulative < -10000:
                return 'high'
            elif negative_percentage > 40 or final_cumulative < -5000:
                return 'medium'
            else:
                return 'low'
                
        except Exception as e:
            self.logger.error(f"خطأ في تقييم مخاطر التدفق النقدي: {e}")
            return 'unknown'
    
    def _get_model_accuracy(self, model_type: str) -> Dict[str, float]:
        """الحصول على دقة النموذج"""
        # هذه دالة مبسطة - يمكن تحسينها بتقييم فعلي للنموذج
        return {
            'accuracy': 0.75,  # دقة افتراضية
            'mae': 100.0,      # متوسط الخطأ المطلق
            'rmse': 150.0      # جذر متوسط مربع الخطأ
        }
    
    def _get_confidence_explanation(self, confidence_level: str) -> str:
        """الحصول على شرح مستوى الثقة"""
        explanations = {
            'high': 'البيانات التاريخية مستقرة والتنبؤ موثوق',
            'medium': 'البيانات التاريخية متوسطة الاستقرار والتنبؤ معقول',
            'low': 'البيانات التاريخية متقلبة والتنبؤ أقل موثوقية'
        }
        return explanations.get(confidence_level, 'غير محدد')

# مثيل عام لنظام التنبؤات
financial_predictor = FinancialPredictor()
