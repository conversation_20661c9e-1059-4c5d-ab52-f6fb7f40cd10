"""
إعدادات البرنامج الأساسية
"""
import os
from pathlib import Path

# مسارات المشروع
BASE_DIR = Path(__file__).parent.parent.parent
SRC_DIR = BASE_DIR / "src"
DATA_DIR = BASE_DIR / "data"
LOGS_DIR = BASE_DIR / "logs"

# إنشاء المجلدات إذا لم تكن موجودة
DATA_DIR.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'name': 'masroof.db',
    'path': DATA_DIR / 'masroof.db',
    'backup_path': DATA_DIR / 'backups',
    'auto_backup': True,
    'backup_interval_days': 7
}

# إعدادات العملة
CURRENCY_CONFIG = {
    'primary': 'IQD',  # الدينار العراقي
    'symbol': 'د.ع',
    'decimal_places': 0,  # الدينار العراقي لا يستخدم كسور عادة
    'thousands_separator': ',',
    'format': '{symbol} {amount:,.0f}'
}

# إعدادات الواجهة
UI_CONFIG = {
    'theme': 'dark',  # dark, light, system
    'language': 'ar',  # ar, en
    'font_family': 'Arial',
    'font_size': 12,
    'window_size': (1200, 800),
    'window_min_size': (800, 600),
    'rtl_support': True
}

# إعدادات الذكاء الصناعي
AI_CONFIG = {
    'enable_predictions': True,
    'prediction_days': 30,
    'min_data_points': 10,
    'confidence_threshold': 0.7,
    'update_interval_hours': 24
}

# إعدادات الإشعارات
NOTIFICATION_CONFIG = {
    'enable_notifications': True,
    'debt_reminder_days': [7, 3, 1],  # تذكير قبل الاستحقاق بـ 7، 3، 1 أيام
    'budget_warning_percentage': 80,  # تحذير عند الوصول لـ 80% من الميزانية
    'daily_summary_time': '20:00'  # ملخص يومي في الساعة 8 مساءً
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'enable_encryption': True,
    'password_min_length': 8,
    'session_timeout_minutes': 30,
    'max_login_attempts': 3,
    'backup_encryption': True
}

# إعدادات التصدير
EXPORT_CONFIG = {
    'default_format': 'xlsx',  # xlsx, csv, pdf
    'include_charts': True,
    'date_format': '%Y-%m-%d',
    'export_path': DATA_DIR / 'exports'
}

# إنشاء مجلد التصدير
EXPORT_CONFIG['export_path'].mkdir(exist_ok=True)

# فئات المصروفات الافتراضية
DEFAULT_EXPENSE_CATEGORIES = [
    'طعام وشراب',
    'مواصلات',
    'سكن وإيجار',
    'فواتير',
    'صحة وطب',
    'تعليم',
    'ترفيه',
    'ملابس',
    'تسوق',
    'وقود',
    'صيانة',
    'أخرى'
]

# فئات الدخل الافتراضية
DEFAULT_INCOME_CATEGORIES = [
    'راتب',
    'مكافآت',
    'استثمارات',
    'أعمال جانبية',
    'هدايا',
    'أخرى'
]

# أنواع الديون
DEBT_TYPES = [
    'قرض شخصي',
    'قرض سيارة',
    'قرض منزل',
    'بطاقة ائتمان',
    'دين شخصي',
    'أخرى'
]

# رسائل النظام
MESSAGES = {
    'ar': {
        'welcome': 'مرحباً بك في برنامج مصروف',
        'login_required': 'يرجى تسجيل الدخول أولاً',
        'invalid_credentials': 'بيانات الدخول غير صحيحة',
        'data_saved': 'تم حفظ البيانات بنجاح',
        'data_deleted': 'تم حذف البيانات بنجاح',
        'backup_created': 'تم إنشاء نسخة احتياطية',
        'export_completed': 'تم تصدير البيانات بنجاح',
        'error_occurred': 'حدث خطأ، يرجى المحاولة مرة أخرى'
    },
    'en': {
        'welcome': 'Welcome to Masroof',
        'login_required': 'Please login first',
        'invalid_credentials': 'Invalid credentials',
        'data_saved': 'Data saved successfully',
        'data_deleted': 'Data deleted successfully',
        'backup_created': 'Backup created successfully',
        'export_completed': 'Data exported successfully',
        'error_occurred': 'An error occurred, please try again'
    }
}

# إعدادات السجلات
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': LOGS_DIR / 'masroof.log',
    'max_size_mb': 10,
    'backup_count': 5
}
