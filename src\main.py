"""
الملف الرئيسي لبرنامج مصروف
"""
import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import customtkinter as ctk
from src.config.settings import UI_CONFIG, LOGGING_CONFIG, MESSAGES
from src.database.database import DatabaseManager
from src.ui.main_window import MainWindow

class MasroofApp:
    """التطبيق الرئيسي لبرنامج مصروف"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        self.setup_ui()
        self.setup_database()
        
    def setup_logging(self):
        """إعداد نظام السجلات"""
        logging.basicConfig(
            level=getattr(logging, LOGGING_CONFIG['level']),
            format=LOGGING_CONFIG['format'],
            handlers=[
                logging.FileHandler(LOGGING_CONFIG['file'], encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # تعيين مظهر CustomTkinter
        ctk.set_appearance_mode(UI_CONFIG['theme'])
        ctk.set_default_color_theme("blue")
        
        # إعداد الخط للغة العربية
        if UI_CONFIG['language'] == 'ar':
            ctk.set_widget_scaling(1.0)
            
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.db_manager = DatabaseManager()
            self.logger.info("تم إعداد قاعدة البيانات بنجاح")
        except Exception as e:
            self.logger.error(f"خطأ في إعداد قاعدة البيانات: {e}")
            sys.exit(1)
    
    def run(self):
        """تشغيل التطبيق"""
        try:
            self.logger.info("بدء تشغيل برنامج مصروف")
            
            # إنشاء النافذة الرئيسية
            self.main_window = MainWindow(self.db_manager)
            
            # تشغيل حلقة الأحداث
            self.main_window.mainloop()
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل التطبيق: {e}")
            sys.exit(1)
        finally:
            # تنظيف الموارد
            if hasattr(self, 'db_manager'):
                self.db_manager.disconnect()
            self.logger.info("تم إغلاق برنامج مصروف")

def main():
    """النقطة الرئيسية لبدء التطبيق"""
    try:
        app = MasroofApp()
        app.run()
    except KeyboardInterrupt:
        print("\nتم إيقاف البرنامج بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"خطأ فادح في التطبيق: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
