"""
المستشار المالي الذكي
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import numpy as np

from ..config.settings import AI_CONFIG, CURRENCY_CONFIG

class SmartAdvisor:
    """المستشار المالي الذكي"""
    
    def __init__(self):
        """تهيئة المستشار الذكي"""
        self.logger = logging.getLogger(__name__)
        self.advice_categories = {
            'saving': 'توفير',
            'spending': 'إنفاق',
            'debt': 'ديون',
            'investment': 'استثمار',
            'budget': 'ميزانية',
            'emergency': 'طوارئ'
        }
    
    def get_financial_advice(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """الحصول على نصائح مالية شاملة"""
        try:
            advice = {
                'status': 'success',
                'overall_score': 0,
                'recommendations': [],
                'priorities': [],
                'warnings': [],
                'achievements': []
            }
            
            # تحليل الوضع المالي العام
            financial_health = self._assess_financial_health(financial_data)
            advice['overall_score'] = financial_health['score']
            advice['health_status'] = financial_health['status']
            
            # نصائح التوفير
            saving_advice = self._get_saving_advice(financial_data)
            advice['recommendations'].extend(saving_advice)
            
            # نصائح الإنفاق
            spending_advice = self._get_spending_advice(financial_data)
            advice['recommendations'].extend(spending_advice)
            
            # نصائح الديون
            debt_advice = self._get_debt_advice(financial_data)
            advice['recommendations'].extend(debt_advice)
            
            # نصائح الميزانية
            budget_advice = self._get_budget_advice(financial_data)
            advice['recommendations'].extend(budget_advice)
            
            # تحديد الأولويات
            advice['priorities'] = self._determine_priorities(advice['recommendations'])
            
            # التحذيرات
            advice['warnings'] = self._generate_warnings(financial_data)
            
            # الإنجازات
            advice['achievements'] = self._identify_achievements(financial_data)
            
            return advice
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على النصائح المالية: {e}")
            return {
                'status': 'error',
                'message': f'حدث خطأ في توليد النصائح: {str(e)}'
            }
    
    def _assess_financial_health(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """تقييم الصحة المالية العامة"""
        try:
            score = 0
            max_score = 100
            
            # تقييم الرصيد
            balance = financial_data.get('current_balance', 0)
            if balance > 0:
                score += 20
            elif balance > -10000:
                score += 10
            
            # تقييم نسبة الادخار
            monthly_income = financial_data.get('monthly_income', 0)
            monthly_expenses = financial_data.get('monthly_expenses', 0)
            
            if monthly_income > 0:
                savings_rate = (monthly_income - monthly_expenses) / monthly_income * 100
                if savings_rate >= 20:
                    score += 25
                elif savings_rate >= 10:
                    score += 15
                elif savings_rate >= 0:
                    score += 5
            
            # تقييم الديون
            total_debt = financial_data.get('total_debt', 0)
            if total_debt == 0:
                score += 20
            elif monthly_income > 0:
                debt_to_income = total_debt / (monthly_income * 12) * 100
                if debt_to_income < 20:
                    score += 15
                elif debt_to_income < 40:
                    score += 10
                elif debt_to_income < 60:
                    score += 5
            
            # تقييم صندوق الطوارئ
            emergency_fund = financial_data.get('emergency_fund', 0)
            if emergency_fund >= monthly_expenses * 6:
                score += 20
            elif emergency_fund >= monthly_expenses * 3:
                score += 15
            elif emergency_fund >= monthly_expenses:
                score += 10
            
            # تقييم تنوع الدخل
            income_sources = financial_data.get('income_sources', 1)
            if income_sources > 2:
                score += 10
            elif income_sources > 1:
                score += 5
            
            # تحديد الحالة
            if score >= 80:
                status = 'excellent'
                status_text = 'ممتاز'
            elif score >= 60:
                status = 'good'
                status_text = 'جيد'
            elif score >= 40:
                status = 'fair'
                status_text = 'مقبول'
            else:
                status = 'poor'
                status_text = 'يحتاج تحسين'
            
            return {
                'score': score,
                'max_score': max_score,
                'percentage': round(score / max_score * 100, 1),
                'status': status,
                'status_text': status_text
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في تقييم الصحة المالية: {e}")
            return {'score': 0, 'status': 'unknown'}
    
    def _get_saving_advice(self, financial_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """نصائح التوفير"""
        try:
            advice = []
            
            monthly_income = financial_data.get('monthly_income', 0)
            monthly_expenses = financial_data.get('monthly_expenses', 0)
            current_savings = financial_data.get('current_savings', 0)
            
            if monthly_income > 0:
                savings_rate = (monthly_income - monthly_expenses) / monthly_income * 100
                
                if savings_rate < 10:
                    advice.append({
                        'category': 'saving',
                        'priority': 'high',
                        'title': 'زيادة معدل الادخار',
                        'description': f'معدل ادخارك الحالي {savings_rate:.1f}%. حاول الوصول إلى 20% على الأقل.',
                        'action': 'قلل المصروفات غير الضرورية وضع خطة ادخار شهرية',
                        'impact': 'high'
                    })
                
                # نصيحة صندوق الطوارئ
                emergency_target = monthly_expenses * 6
                if current_savings < emergency_target:
                    advice.append({
                        'category': 'saving',
                        'priority': 'high',
                        'title': 'بناء صندوق الطوارئ',
                        'description': f'تحتاج إلى {emergency_target:,.0f} د.ع كصندوق طوارئ (6 أشهر من المصروفات)',
                        'action': f'ادخر {(emergency_target - current_savings):,.0f} د.ع إضافية',
                        'impact': 'high'
                    })
                
                # نصيحة الادخار التلقائي
                advice.append({
                    'category': 'saving',
                    'priority': 'medium',
                    'title': 'الادخار التلقائي',
                    'description': 'اعتمد على الادخار التلقائي لضمان الانتظام',
                    'action': 'احول مبلغاً ثابتاً شهرياً إلى حساب الادخار فور استلام الراتب',
                    'impact': 'medium'
                })
            
            return advice
            
        except Exception as e:
            self.logger.error(f"خطأ في نصائح التوفير: {e}")
            return []
    
    def _get_spending_advice(self, financial_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """نصائح الإنفاق"""
        try:
            advice = []
            
            spending_analysis = financial_data.get('spending_analysis', {})
            top_categories = spending_analysis.get('top_categories', {})
            
            # تحليل الفئات الأكثر إنفاقاً
            for category_id, stats in top_categories.items():
                percentage = stats.get('percentage', 0)
                
                if percentage > 40:
                    advice.append({
                        'category': 'spending',
                        'priority': 'high',
                        'title': f'تركز الإنفاق في فئة واحدة',
                        'description': f'{percentage:.1f}% من إنفاقك في فئة واحدة',
                        'action': 'نوع مصروفاتك وراجع الضروريات في هذه الفئة',
                        'impact': 'high'
                    })
            
            # نصائح عامة للإنفاق
            advice.append({
                'category': 'spending',
                'priority': 'medium',
                'title': 'قاعدة 50/30/20',
                'description': 'اتبع قاعدة 50% للضروريات، 30% للرغبات، 20% للادخار',
                'action': 'راجع توزيع مصروفاتك وفقاً لهذه القاعدة',
                'impact': 'medium'
            })
            
            # نصيحة تتبع المصروفات
            advice.append({
                'category': 'spending',
                'priority': 'low',
                'title': 'تتبع المصروفات اليومية',
                'description': 'سجل كل مصروف مهما كان صغيراً',
                'action': 'استخدم تطبيق مصروف لتسجيل جميع المعاملات',
                'impact': 'medium'
            })
            
            return advice
            
        except Exception as e:
            self.logger.error(f"خطأ في نصائح الإنفاق: {e}")
            return []
    
    def _get_debt_advice(self, financial_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """نصائح إدارة الديون"""
        try:
            advice = []
            
            total_debt = financial_data.get('total_debt', 0)
            monthly_income = financial_data.get('monthly_income', 0)
            debts = financial_data.get('debts', [])
            
            if total_debt > 0:
                # نصيحة نسبة الدين إلى الدخل
                if monthly_income > 0:
                    debt_to_income = total_debt / (monthly_income * 12) * 100
                    
                    if debt_to_income > 40:
                        advice.append({
                            'category': 'debt',
                            'priority': 'high',
                            'title': 'نسبة دين عالية',
                            'description': f'نسبة ديونك {debt_to_income:.1f}% من دخلك السنوي',
                            'action': 'ضع خطة عاجلة لتقليل الديون وتجنب ديون جديدة',
                            'impact': 'high'
                        })
                
                # استراتيجية سداد الديون
                if len(debts) > 1:
                    advice.append({
                        'category': 'debt',
                        'priority': 'high',
                        'title': 'استراتيجية كرة الثلج',
                        'description': 'ادفع الحد الأدنى لجميع الديون وركز على الأصغر أولاً',
                        'action': 'رتب ديونك من الأصغر للأكبر وسدد الأصغر بالكامل أولاً',
                        'impact': 'high'
                    })
                
                # تحذير من الفوائد
                high_interest_debts = [d for d in debts if d.get('interest_rate', 0) > 15]
                if high_interest_debts:
                    advice.append({
                        'category': 'debt',
                        'priority': 'high',
                        'title': 'ديون بفوائد عالية',
                        'description': f'لديك {len(high_interest_debts)} دين بفوائد عالية',
                        'action': 'أعد تمويل الديون عالية الفائدة أو سددها أولاً',
                        'impact': 'high'
                    })
            else:
                advice.append({
                    'category': 'debt',
                    'priority': 'low',
                    'title': 'خالي من الديون',
                    'description': 'ممتاز! أنت خالي من الديون',
                    'action': 'حافظ على هذا الوضع وتجنب الديون غير الضرورية',
                    'impact': 'positive'
                })
            
            return advice
            
        except Exception as e:
            self.logger.error(f"خطأ في نصائح الديون: {e}")
            return []
    
    def _get_budget_advice(self, financial_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """نصائح الميزانية"""
        try:
            advice = []
            
            budgets = financial_data.get('budgets', [])
            budget_performance = financial_data.get('budget_performance', {})
            
            if not budgets:
                advice.append({
                    'category': 'budget',
                    'priority': 'medium',
                    'title': 'إنشاء ميزانية',
                    'description': 'لا توجد ميزانية محددة لمصروفاتك',
                    'action': 'ضع ميزانية شهرية لكل فئة من فئات الإنفاق',
                    'impact': 'high'
                })
            else:
                # تحليل أداء الميزانية
                over_budget_count = sum(1 for perf in budget_performance.values() if perf.get('percentage', 0) > 100)
                
                if over_budget_count > 0:
                    advice.append({
                        'category': 'budget',
                        'priority': 'medium',
                        'title': 'تجاوز الميزانية',
                        'description': f'تجاوزت الميزانية في {over_budget_count} فئة',
                        'action': 'راجع الميزانيات المتجاوزة وعدلها أو قلل الإنفاق',
                        'impact': 'medium'
                    })
            
            # نصيحة المراجعة الدورية
            advice.append({
                'category': 'budget',
                'priority': 'low',
                'title': 'مراجعة دورية للميزانية',
                'description': 'راجع ميزانيتك شهرياً وعدلها حسب الحاجة',
                'action': 'خصص وقتاً شهرياً لمراجعة الأداء المالي',
                'impact': 'medium'
            })
            
            return advice
            
        except Exception as e:
            self.logger.error(f"خطأ في نصائح الميزانية: {e}")
            return []
    
    def _determine_priorities(self, recommendations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """تحديد أولويات النصائح"""
        try:
            # ترتيب النصائح حسب الأولوية والتأثير
            priority_order = {'high': 3, 'medium': 2, 'low': 1}
            impact_order = {'high': 3, 'medium': 2, 'low': 1, 'positive': 0}
            
            sorted_recommendations = sorted(
                recommendations,
                key=lambda x: (
                    priority_order.get(x.get('priority', 'low'), 1),
                    impact_order.get(x.get('impact', 'low'), 1)
                ),
                reverse=True
            )
            
            return sorted_recommendations[:5]  # أهم 5 نصائح
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديد الأولويات: {e}")
            return recommendations[:5]
    
    def _generate_warnings(self, financial_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """توليد التحذيرات المالية"""
        try:
            warnings = []
            
            # تحذير الرصيد المنخفض
            current_balance = financial_data.get('current_balance', 0)
            if current_balance < 0:
                warnings.append({
                    'type': 'critical',
                    'title': 'رصيد سالب',
                    'message': 'رصيدك الحالي سالب، تحتاج لإجراء عاجل'
                })
            elif current_balance < 10000:
                warnings.append({
                    'type': 'warning',
                    'title': 'رصيد منخفض',
                    'message': 'رصيدك منخفض، احرص على زيادة الادخار'
                })
            
            # تحذير الديون المستحقة
            overdue_debts = financial_data.get('overdue_debts', [])
            if overdue_debts:
                warnings.append({
                    'type': 'critical',
                    'title': 'ديون متأخرة',
                    'message': f'لديك {len(overdue_debts)} دين متأخر السداد'
                })
            
            return warnings
            
        except Exception as e:
            self.logger.error(f"خطأ في توليد التحذيرات: {e}")
            return []
    
    def _identify_achievements(self, financial_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """تحديد الإنجازات المالية"""
        try:
            achievements = []
            
            # إنجاز الادخار
            savings_rate = financial_data.get('savings_rate', 0)
            if savings_rate >= 20:
                achievements.append({
                    'type': 'saving',
                    'title': 'مدخر ممتاز',
                    'description': f'تدخر {savings_rate:.1f}% من دخلك - هذا ممتاز!'
                })
            
            # إنجاز خلو من الديون
            total_debt = financial_data.get('total_debt', 0)
            if total_debt == 0:
                achievements.append({
                    'type': 'debt_free',
                    'title': 'خالي من الديون',
                    'description': 'تهانينا! أنت خالي من الديون'
                })
            
            # إنجاز صندوق الطوارئ
            emergency_fund = financial_data.get('emergency_fund', 0)
            monthly_expenses = financial_data.get('monthly_expenses', 0)
            if emergency_fund >= monthly_expenses * 6:
                achievements.append({
                    'type': 'emergency_fund',
                    'title': 'صندوق طوارئ قوي',
                    'description': 'لديك صندوق طوارئ يكفي لأكثر من 6 أشهر'
                })
            
            return achievements
            
        except Exception as e:
            self.logger.error(f"خطأ في تحديد الإنجازات: {e}")
            return []

# مثيل عام للمستشار الذكي
smart_advisor = SmartAdvisor()
