# دليل المستخدم - برنامج مصروف

## 📖 مقدمة
مرحباً بك في برنامج **مصروف** - برنامج إدارة المصروفات الشخصية المتطور مع الذكاء الصناعي. هذا الدليل سيساعدك على الاستفادة الكاملة من جميع ميزات البرنامج.

## 🚀 البدء السريع

### 1. التثبيت
```bash
# استنساخ المشروع
git clone https://github.com/your-username/masroof.git
cd masroof

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # على Linux/macOS
# أو
venv\Scripts\activate     # على Windows

# تثبيت المتطلبات
pip install -r requirements.txt
```

### 2. التشغيل
```bash
# الطريقة الأولى
python run.py

# الطريقة الثانية
python src/main.py
```

## 🎯 الميزات الرئيسية

### 📊 لوحة التحكم
- عرض الرصيد الإجمالي
- ملخص الدخل والمصروفات الشهرية
- إجمالي الديون
- رسوم بيانية تفاعلية

### 💳 إدارة المعاملات
- إضافة معاملات الدخل والمصروفات
- تصنيف المعاملات حسب الفئات
- البحث والفلترة المتقدمة
- إرفاق الإيصالات والملاحظات

### 💸 إدارة الديون
- تتبع جميع أنواع الديون
- حساب الفوائد والأقساط
- تنبيهات مواعيد الاستحقاق
- خطط السداد الذكية

### 📋 الميزانيات
- وضع ميزانيات للفئات المختلفة
- مراقبة الإنفاق مقابل الميزانية
- تنبيهات تجاوز الحدود
- تحليل الأداء

### 📈 التقارير والتحليلات
- تقارير مفصلة للدخل والمصروفات
- رسوم بيانية متنوعة
- تحليل الاتجاهات
- مقارنات زمنية

### 🤖 الذكاء الصناعي
- تحليل أنماط الإنفاق
- التنبؤ بالمصروفات المستقبلية
- نصائح مالية ذكية
- تحذيرات استباقية

## 🔧 الإعدادات

### العملة
- العملة الافتراضية: الدينار العراقي (د.ع)
- يمكن تغيير العملة من الإعدادات

### الإشعارات
- تفعيل/تعطيل الإشعارات
- تخصيص أوقات التذكير
- إعدادات التنبيهات

### الأمان
- حماية البيانات بكلمة مرور
- تشفير قاعدة البيانات
- نسخ احتياطية تلقائية

## 📱 واجهة المستخدم

### الشريط الجانبي
- **📊 لوحة التحكم**: الصفحة الرئيسية
- **💳 المعاملات**: إدارة الدخل والمصروفات
- **💸 الديون**: تتبع وإدارة الديون
- **📋 الميزانيات**: وضع ومراقبة الميزانيات
- **📈 التقارير**: تقارير وتحليلات مفصلة
- **⚙️ الإعدادات**: إعدادات البرنامج

### اختصارات لوحة المفاتيح
- `Ctrl+N`: إضافة معاملة جديدة
- `Ctrl+S`: حفظ
- `Ctrl+E`: تصدير البيانات
- `F5`: تحديث البيانات

## 💡 نصائح للاستخدام الأمثل

### 1. التسجيل المنتظم
- سجل جميع المعاملات يومياً
- استخدم الفئات المناسبة
- أضف ملاحظات توضيحية

### 2. استخدام الميزانيات
- ضع ميزانيات واقعية
- راجعها شهرياً
- عدلها حسب الحاجة

### 3. الاستفادة من الذكاء الصناعي
- راجع التحليلات بانتظام
- اتبع النصائح المقترحة
- استخدم التنبؤات للتخطيط

### 4. النسخ الاحتياطية
- فعل النسخ الاحتياطية التلقائية
- احتفظ بنسخ في أماكن آمنة
- اختبر استعادة البيانات

## 🔒 الأمان والخصوصية

### حماية البيانات
- جميع البيانات محفوظة محلياً
- تشفير قوي للبيانات الحساسة
- لا يتم إرسال بيانات للخارج

### كلمات المرور
- استخدم كلمة مرور قوية
- غيرها بانتظام
- لا تشاركها مع أحد

## 📊 التصدير والاستيراد

### التصدير
- Excel (.xlsx)
- CSV (.csv)
- PDF (التقارير)

### الاستيراد
- CSV من البنوك
- Excel من برامج أخرى
- تنسيقات مخصصة

## 🆘 حل المشاكل الشائعة

### البرنامج لا يبدأ
1. تأكد من تثبيت Python 3.11+
2. تحقق من تثبيت المتطلبات
3. راجع ملف السجلات

### بطء في الأداء
1. قم بتنظيف البيانات القديمة
2. أعد تشغيل البرنامج
3. تحقق من مساحة القرص

### مشاكل في قاعدة البيانات
1. أنشئ نسخة احتياطية
2. أعد إنشاء قاعدة البيانات
3. استعد البيانات من النسخة الاحتياطية

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://masroof.app
- المنتدى: https://forum.masroof.app

### الإبلاغ عن الأخطاء
- استخدم نظام التذاكر في الموقع
- أرفق ملف السجلات
- وصف المشكلة بالتفصيل

## 🔄 التحديثات

### التحقق من التحديثات
- يتم التحقق تلقائياً
- إشعارات عند توفر تحديثات
- تحديث آمن مع حفظ البيانات

### سجل التغييرات
- راجع ملف CHANGELOG.md
- ميزات جديدة
- إصلاحات الأخطاء

## 🎓 دروس تعليمية

### للمبتدئين
1. إعداد البرنامج لأول مرة
2. إضافة أول معاملة
3. إنشاء ميزانية بسيطة

### للمستخدمين المتقدمين
1. استخدام التحليلات المتقدمة
2. تخصيص التقارير
3. أتمتة المهام

## 📚 مصادر إضافية

### الوثائق التقنية
- API Documentation
- Database Schema
- Plugin Development

### المجتمع
- مجموعة فيسبوك
- قناة تيليجرام
- منتدى المطورين

---

**شكراً لاستخدامك برنامج مصروف! 💰**

نتمنى أن يساعدك في تحقيق أهدافك المالية وإدارة مصروفاتك بكفاءة.
